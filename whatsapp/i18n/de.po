# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "%(create_count)s were created, %(update_count)s were updated"
msgstr ""
"%(create_count)s wurden erstellt, %(update_count)s wurden aktualisiert"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (Kopie)"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(template_name)s [%(account_name)s]"
msgstr "%(template_name)s [%(account_name)s]"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "'%(field)s' does not seem to be a valid field path on %(model)s"
msgstr "„%(field)s“ scheint kein gültiger Feldpfad in %(model)s zu sein"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ", ... (%s Others)"
msgstr " ... (%s weitere)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_kanban
msgid ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"
msgstr ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"
msgstr ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "<span class=\"o_stat_text\">Chats</span>"
msgstr "<span class=\"o_stat_text\">Chats</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"
msgstr ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid ""
"<strong>Invalid number: </strong>\n"
"                            <span>make sure to set a country on the Contact or to specify the country code.</span>"
msgstr ""
"<strong>Ungültige Nummer:</strong>\n"
"                             <span> Vergewissern Sie sich, dass sie ein Land für </span>"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A new WhatsApp channel is created for this document"
msgstr "Ein neuer WhatsApp-Kanal wurde für dieses Dokument erstellt"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid ""
"A new template was sent on %(record_link)s.<br>Future replies will be "
"transferred to a new chat."
msgstr ""
"Eine neue Vorlage wurde an %(record_link)s gesendet.<br>Zukünftige Antworten"
" werden in einen neuen Chat übertragen."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A phone number is required for WhatsApp channels %(channel_names)s"
msgstr ""
"Für WhatsApp-Kanäle %(channel_names)s ist eine Telefonnummer erforderlich"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__token
msgid "Access Token"
msgstr "Zugriffstoken"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Accessible to all Users"
msgstr "Für alle Benutzer zugänglich"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Account"
msgstr "Konto"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__account
msgid "Account Error"
msgstr "Kontofehler"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__account_uid
msgid "Account ID"
msgstr "Konto-ID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__active
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__active
msgid "Active"
msgstr "Aktiv"

#. module: whatsapp
#: model:res.groups,name:whatsapp.group_whatsapp_admin
msgid "Administrator"
msgstr "Administrator"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__af
msgid "Afrikaans"
msgstr "Afrikaans"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sq
msgid "Albanian"
msgstr "Albanisch"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "All dynamic urls must have a placeholder."
msgstr "Alle dynamischen URLs müssen einen Platzhalter haben."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Allow Multi"
msgstr "App-übergreifende Nutzung erlauben"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__allowed_company_ids
msgid "Allowed Company"
msgstr "Zulässiges Unternehmen"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Allowed companies"
msgstr "Zulässige Unternehmen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_uid
msgid "App ID"
msgstr "App-ID"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_secret
msgid "App Secret"
msgstr "App-Geheimnis"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model_id
msgid "Applies to"
msgstr "Gilt für"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__approved
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Approved"
msgstr "Genehmigt"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ar
msgid "Arabic"
msgstr "Arabisch"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Archived"
msgstr "Archiviert"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__attachment_id
msgid "Attachment"
msgstr "Dateianhang"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_attachment_count
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "Attachment mimetype is not supported by WhatsApp: %s."
msgstr "MIME-Typ des Anhangs wird nicht von WhatsApp unterstützt: %s."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__authentication
msgid "Authentication"
msgstr "Authentifizierung"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__template_type
msgid ""
"Authentication - One-time passwords that your customers use to authenticate a transaction or login.\n"
"Marketing - Promotions or information about your business, products or services. Or any message that isn't utility or authentication.\n"
"Utility - Messages about a specific transaction, account, order or customer request."
msgstr ""
"Authentifizierung – Einmalige Passwörter, die Ihre Kunden zur Authentifizierung einer Transaktion oder Anmeldung verwenden.\n"
"Marketing – Werbeaktionen oder Informationen über Ihr Unternehmen, Ihre Produkte oder Dienstleistungen. Oder jede Nachricht, die nicht der Nützlichkeit oder Authentifizierung dient.\n"
"Nutzwert – Nachrichten über eine bestimmte Transaktion, ein Konto, eine Bestellung oder eine Kundenanfrage."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__az
msgid "Azerbaijani"
msgstr "Aser­bai­d­scha­nisch"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_base
msgid "Base"
msgstr "Basis"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bn
msgid "Bengali"
msgstr "Bengalisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__blacklisted
msgid "Blacklisted Phone Number"
msgstr "Telefonnummer auf der schwarzen Liste"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__body
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__body
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Body"
msgstr "Nachrichtentext"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Body variables should start at 1 and not skip any number, missing %d"
msgstr ""
"Textvariablen sollten bei 1 beginnen und keine Zahl überspringen, %d fehlt"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__bounced
msgid "Bounced"
msgstr "Unzustellbar"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bg
msgid "Bulgarian"
msgstr "Bulgarisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__button_id
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__button
msgid "Button"
msgstr "Schaltfläche"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__name
msgid "Button Text"
msgstr "Schaltflächentext"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_1
msgid "Button Url 1"
msgstr "Schaltflächen-URL 1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_2
msgid "Button Url 2"
msgstr "Schaltflächen-URL 2"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_button_unique_name_per_template
msgid "Button names must be unique in a given template"
msgstr "Schaltflächenname muss in einer bestimmten Vorlage einzigartig sein"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Button variables must be linked to a button."
msgstr "Schaltflächenvariablen müssen mit einer Schaltfläche verknüpft sein."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__button_ids
msgid "Buttons"
msgstr "Schaltflächen"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Buttons may only contain one placeholder."
msgstr "Schaltflächen können nur einen Platzhalter enthalten."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__call_number
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__phone_number
msgid "Call Number"
msgstr "Rufnummer"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__callback_url
msgid "Callback URL"
msgstr "Rückruf-URL"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid ""
"Can't send message as it has been 24 hours since the last message of the "
"User."
msgstr ""
"Nachrichten können nicht versendet werden, da seit der letzten Nachricht des"
" Benutzers 24 Stunden vergangen sind."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
msgid "Cancel"
msgstr "Stornieren"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Cancel WhatsApp"
msgstr "WhatsApp abbrechen"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__cancel
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ca
msgid "Catalan"
msgstr "Katalanisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_type
msgid "Category"
msgstr "Kategorie"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel_member
msgid "Channel Member"
msgstr "Kanalmitglied"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Kanaltyp"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Der Chat ist privat und nur zwischen 2 Personen. Die Gruppe ist privat für "
"die eingeladenen Personen. Einem Kanal kann frei beigetreten werden "
"(abhängig von seiner Konfiguration)."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_cn
msgid "Chinese (CHN)"
msgstr "Chinesisch (CHN)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_hk
msgid "Chinese (HKG)"
msgstr "Chinesisch (HKG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_tw
msgid "Chinese (TAI)"
msgstr "Chinesisch (TAI)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.ir_actions_server_view_form_whatsapp
msgid "Choose a template..."
msgstr "Eine Vorlage auswählen ..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Close"
msgstr "Schließen"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_configuration_menu
msgid "Configuration"
msgstr "Konfiguration"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Configure Meta Accounts"
msgstr "Meta-Konten konfigurieren"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "Configure Templates"
msgstr "Vorlagen konfigurieren"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Configure Whatsapp Business Account"
msgstr "WhatsApp-Business-Konto konfigurieren"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Create Date"
msgstr "Erstellt am"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Create an Account on the"
msgstr "Erstellen Sie ein Konto auf der Plattform von"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Created On"
msgstr "Erstellt am"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Credentials look good!"
msgstr "Anmeldedaten in Ordnung!"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hr
msgid "Croatian"
msgstr "Kroatisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__cs
msgid "Czech"
msgstr "Tschechisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__da
msgid "Danish"
msgstr "Dänisch"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Default Users"
msgstr "Standardbenutzer"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__deleted
msgid "Deleted"
msgstr "Gelöscht"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__delivered
msgid "Delivered"
msgstr "Zugestellt"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Delivered Messages"
msgstr "Zugestellte Nachrichten"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__disabled
msgid "Disabled"
msgstr "Deaktiviert"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Disallow Multi"
msgstr "App-übergreifende Nutzung verweigern"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel
msgid "Discussion Channel"
msgstr "Diskussionskanal"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__document
msgid "Document"
msgstr "Dokument"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_ids
msgid "Document IDs"
msgstr "Dokument-IDs"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_model
msgid "Document Model Name"
msgstr "Name des Dokumentmodells"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload failed, please retry after sometime."
msgstr ""
"Das Hochladen des Dokuments ist fehlgeschlagen, bitte versuchen Sie es "
"später erneut."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload session open failed, please retry after sometime."
msgstr ""
"Das Hochladen des Dokuments bei offener Sitzung ist fehlgeschlagen, bitte "
"versuchen Sie es später erneut."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__draft
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Draft"
msgstr "Entwurf"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_unique_name_account_template
msgid "Duplicate template is not allowed for one Meta account."
msgstr "Duplizierte Vorlage ist nicht für ein Meta-Konto erlaubt."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nl
msgid "Dutch"
msgstr "Niederländisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__dynamic
msgid "Dynamic"
msgstr "Dynamisch"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Dynamic button variable name must be the same as its respective button's "
"name"
msgstr ""
"Variabelname der dynamischen Schaltfläche muss derselbe Name wir ihre "
"entsprechende Schaltfläche sein"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_message_unique_msg_uid
msgid "Each whatsapp message should correspond to a single message uuid."
msgstr ""
"Jede Whatsapp-Nachricht sollte einer einzigen Nachrichten-UUID entsprechen."

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_thread
msgid "Email Thread"
msgstr "E-Mail-Thread"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/phone_field/phone_field.js:0
msgid "Enable WhatsApp"
msgstr "WhatsApp aktivieren"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en
msgid "English"
msgstr "Englisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_gb
msgid "English (UK)"
msgstr "Englisch (UK)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_us
msgid "English (US)"
msgstr "Englisch (US)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__error_msg
msgid "Error Message"
msgstr "Fehlermeldung"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__et
msgid "Estonian"
msgstr "Estnisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__error
msgid "Failed"
msgstr "Fehlgeschlagen"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Failed Messages"
msgstr "Fehlgeschlagene Nachrichten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_reason
msgid "Failure Reason"
msgstr "Fehlerursache"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_type
msgid "Failure Type"
msgstr "Fehlertyp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_name
msgid "Field"
msgstr "Feld"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__field
msgid "Field of Model"
msgstr "Feld des Modells"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Field template variables %(var_names)s must be associated with a field."
msgstr ""
"Feldvorlagenvariablen %(var_names)s müssen mit einem Feld verknüpft sein."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "File type %(file_type)s not supported for header type %(header_type)s"
msgstr ""
"Dateityp %(file_type)s wird nicht vom Kopfzeilentyp %(header_type)s "
"unterstützt"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fil
msgid "Filipino"
msgstr "Philippinisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fi
msgid "Finnish"
msgstr "Finnisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_follower_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_partner_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__footer_text
msgid "Footer Message"
msgstr "Fußzeilennachricht"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__free_text
msgid "Free Text"
msgstr "Freitext"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_1
msgid "Free Text 1"
msgstr "Freitext 1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_10
msgid "Free Text 10"
msgstr "Freitext 10"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_2
msgid "Free Text 2"
msgstr "Freitext 2"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_3
msgid "Free Text 3"
msgstr "Freitext 3"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_4
msgid "Free Text 4"
msgstr "Freitext 4"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_5
msgid "Free Text 5"
msgstr "Freitext 5"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_6
msgid "Free Text 6"
msgstr "Freitext 6"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_7
msgid "Free Text 7"
msgstr "Freitext 7"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_8
msgid "Free Text 8"
msgstr "Freitext 8"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_9
msgid "Free Text 9"
msgstr "Freitext 9"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__free_text_json
msgid "Free Text Template Parameters"
msgstr "Parameter für Freitextvorlage"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Free Text template variables must have a demo value."
msgstr "Freitextvorlage muss einen Demo-Wert haben."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Free text variable in the header should be {{1}}"
msgstr "Freitextvariable in der Kopfzeile sollte {{1}} sein"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fr
msgid "French"
msgstr "Französisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ka
msgid "Georgian"
msgstr "Georgisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__de
msgid "German"
msgstr "Deutsch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__el
msgid "Greek"
msgstr "Griechisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__green
msgid "Green"
msgstr "Grün"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels and whatsapp."
msgstr ""
"Gruppenautorisierung und automatische Gruppenabonnements werden nur für "
"Kanäle und WhatsApp unterstützt."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__gu
msgid "Gujarati"
msgstr "Gujarati"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_action
msgid "Has Action"
msgstr "Hat Aktion"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_invalid_button_number
msgid "Has Invalid Button Number"
msgstr "Hat ungültige Schaltflächennummer"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__has_invalid_number
msgid "Has Invalid Number"
msgstr "Hat ungültige Nummer"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__has_message
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ha
msgid "Hausa"
msgstr "Haussa"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__header
msgid "Header"
msgstr "Kopfzeile"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__header_text_1
msgid "Header Free Text"
msgstr "Freitext der Kopfzeile"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_type
msgid "Header Type"
msgstr "Kopfzeilentyp"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document is required"
msgstr "Kopfzeilendokument ist erforderlich"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document or report is required"
msgstr "Kopfzeilendokument oder Bericht ist erforderlich"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__he
msgid "Hebrew"
msgstr "Hebräisch"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Hello {{1}}, here is your order with the reference {{2}} ..."
msgstr "Hallo {{1}}, hier ist Ihr Auftrag mit der Referenz {{2}} ..."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hi
msgid "Hindi"
msgstr "Hindi"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hu
msgid "Hungarian"
msgstr "Ungarisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__id
msgid "ID"
msgstr "ID"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_recoverable
msgid "Identified Error"
msgstr "Identifizierter Fehler"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "If checked, the WhatsApp category is open in the discuss sidebar"
msgstr ""
"Falls markiert, ist die WhatsApp-Kategorie in der Dialog-Seitenleiste "
"verfügbar"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__image
msgid "Image"
msgstr "Bild"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__in_appeal
msgid "In Appeal"
msgstr "In Revision"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__outgoing
msgid "In Queue"
msgstr "In der Warteschlange"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__inbound
msgid "Inbound"
msgstr "Eingehend"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__id
msgid "Indonesian"
msgstr "Indonesisch"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "Insert variable"
msgstr "Variable einsetzen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__invalid_phone_number_count
msgid "Invalid Phone Number Count"
msgstr "Anzahl ungültiger Telefonnummern"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ga
msgid "Irish"
msgstr "Irisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_button_dynamic
msgid "Is Button Dynamic"
msgstr "Ist Schaltfläche dynamisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_is_follower
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_header_free_text
msgid "Is Header Free Text"
msgstr "Ist Kopfzeile Freitext"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__batch_mode
msgid "Is Multiple Records"
msgstr "Ist Mehrfachdatensatz"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_active
msgid "Is Whatsapp Channel Active"
msgstr "Ist WhatsApp-Kanal aktiv"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__it
msgid "Italian"
msgstr "Italienisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ja
msgid "Japanese"
msgstr "Japanisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kn
msgid "Kannada"
msgstr "Kannada"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kk
msgid "Kazakh"
msgstr "Kasachisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__rw_rw
msgid "Kinyarwanda"
msgstr "Kinyarwanda"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ko
msgid "Korean"
msgstr "Koreanisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ky_kg
msgid "Kyrgyz (Kyrgyzstan)"
msgstr "Kirgisisch (Kirgisistan)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__lang_code
msgid "Language"
msgstr "Sprache"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lo
msgid "Lao"
msgstr "Laotisch"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Month"
msgstr "Letzter Monat"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__last_wa_mail_message_id
msgid "Last WA Partner Mail Message"
msgstr "Letzte WA-Nachricht mit Partner"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Week"
msgstr "Letzte Woche"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Year"
msgstr "Letztes Jahr"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lv
msgid "Latvian"
msgstr "Lettisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__limit_exceeded
msgid "Limit Exceeded"
msgstr "Limit überschritten"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lt
msgid "Lithuanian"
msgstr "Litauisch"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__location
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__location
msgid "Location"
msgstr "Standort"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location Latitude and Longitude %(latitude)s / %(longitude)s is not in "
"proper format."
msgstr ""
"Die Breiten- und Längengrade %(latitude)s / %(longitude)s des Standorts sind"
" nicht im richtigen Format."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Location variable should be 'name', 'address', 'latitude' or 'longitude'. "
"Cannot parse '%(placeholder)s'"
msgstr ""
"Standortvariable sollte „name“, „address“, „latitude“ oder „longitude“ sein."
" „%(placeholder)s“ konnte nicht geparst werden"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location variables should only exist when a \"location\" header is selected."
msgstr ""
"Standortvariablen sollten nur existieren, wenn als Kopfzeile „Standort“ "
"ausgewählt wurde."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mk
msgid "Macedonian"
msgstr "Mazedonisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mail_message_id
msgid "Mail Message"
msgstr "Mail-Nachricht"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ms
msgid "Malay"
msgstr "Malaiisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ml
msgid "Malayalam"
msgstr "Malayalam"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mr
msgid "Marathi"
msgstr "Marathisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__marketing
msgid "Marketing"
msgstr "Marketing"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 1 Call Number button allowed."
msgstr "Maximal 1 Schaltfläche für Rufnummer erlaubt."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 10 buttons allowed."
msgstr "Maximal 10 Schaltflächen erlaubt."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 2 URL buttons allowed."
msgstr "Maximal 2 URL-Schaltflächen erlaubt."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Members"
msgstr "Mitglieder"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_message
msgid "Message"
msgstr "Nachricht"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__templates_count
msgid "Message Count"
msgstr "Anzahl Nachrichten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__preview_whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__preview_whatsapp
msgid "Message Preview"
msgstr "Nachrichtenvorschau"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Message Statistics Of %(template_name)s"
msgstr "Nachrichtenstatistik von %(template_name)s"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__message_type
msgid "Message Type"
msgstr "Nachrichtentyp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_ids
#: model:ir.ui.menu,name:whatsapp.whatsapp_message_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Messages"
msgstr "Nachrichten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__messages_count
msgid "Messages Count"
msgstr "Anzahl Nachrichten"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Meta for Developers"
msgstr "Meta für Entwickler"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number_formatted
msgid "Mobile Number Formatted"
msgstr "Handynummer formatiert"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Model"
msgstr "Modell"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__model
msgid "Model Name"
msgstr "Modellname"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "Monitor all recent outgoing and incoming messages"
msgstr "Überwachen Sie alle aktuellen ausgehenden und eingehenden Nachrichten"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "My Templates"
msgstr "Meine Vorlagen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__name
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Name"
msgstr "Name"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__network
msgid "Network Error"
msgstr "Netzwerkfehler"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "No Account Configured yet!"
msgstr "Noch kein Konto konfiguriert!"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "No Templates Found!"
msgstr "Keine Vorlage gefunden!"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "No WhatsApp Messages found"
msgstr "Keine WhatsApp-Nachrichten gefunden"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "No approved WhatsApp Templates are available for this model."
msgstr "Für dieses Modell sind keine genehmigten WhatsApp-Vorlagen verfügbar."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/web/channel_selector_patch.js:0
msgid "No results found"
msgstr "Keine Ergebnisse gefunden"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Non-descript Error"
msgstr "Nicht beschreibender Fehler"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__none
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__none
msgid "None"
msgstr "Keine"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nb
msgid "Norwegian"
msgstr "Norwegisch"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Notifications"
msgstr "Benachrichtigungen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__notify_user_ids
msgid "Notify User"
msgstr "Benutzer benachrichtigen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text
msgid "Number of free text"
msgstr "Anzahl Freitexte"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text_button
msgid "Number of free text Buttons"
msgstr "Anzahl der Freitextschaltflächen"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only 10 free text is allowed in body of template"
msgstr "Im Vorlagentext sind nur 10 Freitexte erlaubt"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Only dynamic urls may have a placeholder."
msgstr "Nur dynamische URLs können einen Platzhalter haben."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid "Only one attachment is allowed for each message"
msgstr "Für jede Nachricht ist nur ein Anhang erlaubt"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only templates using media header types may have header documents"
msgstr ""
"Nur Vorlagen, die Medienkopfzeilentypen verwenden, können "
"Kopfzeilendokumente haben."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_unrecoverable
msgid "Other Technical Error"
msgstr "Anderer technischer Fehler"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__outbound
msgid "Outbound"
msgstr "Ausgehend"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid "Partner created by incoming WhatsApp message."
msgstr "Partner durch eingehende WhatsApp-Nachricht erstellt."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__paused
msgid "Paused"
msgstr "Pausiert"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Pending"
msgstr "Ausstehend"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending_deletion
msgid "Pending Deletion"
msgstr "Ausstehende Löschung"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fa
msgid "Persian"
msgstr "Persisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__phone
msgid "Phone"
msgstr "Telefon"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__phone_field
msgid "Phone Field"
msgstr "Telefonfeld"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_number
msgid "Phone Number"
msgstr "Telefonnummer"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__phone_uid
msgid "Phone Number ID"
msgstr "ID der Telefonnummer"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Phone number Id is wrong."
msgstr "ID der Telefonnummer ist falsch."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Pick an Account..."
msgstr "Ein Konto auswählen ..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Pick users to notify..."
msgstr "Zu benachrichtigende Benutzer auswählen ..."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__name
msgid "Placeholder"
msgstr "Platzhalter"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr ""
"Bitte geben Sie bei der Suche nach einer Telefon-/Handynummer mindestens 3 "
"Zeichen ein."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pl
msgid "Polish"
msgstr "Polnisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__portal_url
msgid "Portal Link"
msgstr "Portallink"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_br
msgid "Portuguese (BR)"
msgstr "Portugiesisch (BR)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_pt
msgid "Portuguese (POR)"
msgstr "Portugiesisch (POR)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Preview"
msgstr "Vorschau"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Preview WhatsApp"
msgstr "WhatsApp-Vorschau"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_preview
msgid "Preview template"
msgstr "Vorlagenvorschau"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pa
msgid "Punjabi"
msgstr "Panjabi"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__quality
msgid "Quality"
msgstr "Qualität"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__quick_reply
msgid "Quick Reply"
msgstr "Schnelle Antwort"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__rating_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__read
msgid "Read"
msgstr "Lesen"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Read Messages"
msgstr "Nachrichten lesen"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Reason : %s"
msgstr "Ursache: %s"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__received
msgid "Received"
msgstr "Erhalten"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Receiving Messages"
msgstr "Nachrichten erhalten"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__red
msgid "Red"
msgstr "Rot"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__rejected
msgid "Rejected"
msgstr "Abgelehnt"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "Related %(model_name)s: "
msgstr "Zugehöriges %(model_name)s:"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model
msgid "Related Document Model"
msgstr "Zugehöriges Dokumentmodell"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__wa_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__wa_message_ids
msgid "Related WhatsApp Messages"
msgstr "Zugehörige WhatsApp-Nachrichten"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__replied
msgid "Replied"
msgstr "Beantwortet"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__report_id
msgid "Report"
msgstr "Bericht"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Reset to draft"
msgstr "Auf Entwurf zurücksetzen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__parent_id
msgid "Response To"
msgstr "Antwort an"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Retry"
msgstr "Wiederholen"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ro
msgid "Romanian"
msgstr "Rumänisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ru
msgid "Russian"
msgstr "Russisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__demo_value
msgid "Sample Value"
msgstr "Beispielwert"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/web/messaging_menu_patch.xml:0
msgid "Search WhatsApp Channel"
msgstr "WhatsApp-Kanal suchen"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid "See all options"
msgstr "Alle Optionen ansehen"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send Message"
msgstr "Nachricht senden"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__ir_actions_server__state__whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send WhatsApp"
msgstr "WhatsApp senden"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.js:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.js:0
msgid "Send WhatsApp Message"
msgstr "WhatsApp-Nachricht senden"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr "Assistent zum Senden von WhatsApps"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Send and receive message through your WhatsApp Business account."
msgstr ""
"Senden und empfangen Sie Nachrichten mit Ihrem WhatsApp-Business-Konto."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Sending Messages"
msgstr "Nachrichten senden"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__sent
msgid "Sent"
msgstr "Gesendet"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent Messages"
msgstr "Gesendete Nachrichten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent To"
msgstr "Gesendet an"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Sent to"
msgstr "Gesendet an"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__sequence
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sr
msgid "Serbian"
msgstr "Serbisch"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_ir_actions_server
msgid "Server Action"
msgstr "Serveraktion"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sk
msgid "Slovak"
msgstr "Slowakisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sl
msgid "Slovenian"
msgstr "Slowenisch"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid ""
"Something went wrong when contacting WhatsApp, please try again later. If "
"this happens frequently, contact support."
msgstr ""
"Bei der Kontaktaufnahme mit WhatsApp ist etwas schiefgelaufen, bitte "
"versuchen Sie es später noch einmal. Wenn dies häufig vorkommt, kontaktieren"
" Sie den Support."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es
msgid "Spanish"
msgstr "Spanisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_ar
msgid "Spanish (ARG)"
msgstr "Spanisch (ARG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_mx
msgid "Spanish (MEX)"
msgstr "Spanisch (MEX)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_es
msgid "Spanish (SPA)"
msgstr "Spanisch (ES)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__state
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "State"
msgstr "Status"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__static
msgid "Static"
msgstr "Statisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__status
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Status"
msgstr "Status"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Submit for Approval"
msgstr "Zur Genehmigung einreichen"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sw
msgid "Swahili"
msgstr "Swahili"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sv
msgid "Swedish"
msgstr "Schwedisch"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Sync Template"
msgstr "Vorlage synchronisieren"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Synchronize Templates"
msgstr "Vorlagen synchronisieren"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ta
msgid "Tamil"
msgstr "Tamil"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__te
msgid "Telugu"
msgstr "Telugu"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Template"
msgstr "Vorlage"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"Template %(template_name)s holds a wrong configuration for 'phone field'\n"
"%(error_msg)s"
msgstr ""
"Vorlage %(template_name)s hat eine falsche Konfiguration für „phone field“\n"
"%(error_msg)s"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_button_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_variable_view_form
msgid "Template Button"
msgstr "Vorlagenschaltfläche"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Template Guidelines"
msgstr "Vorlagenrichtlinien"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_text
msgid "Template Header Text"
msgstr "Kopfzeilentext der Vorlage"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_name
msgid "Template Name"
msgstr "Vorlagenname"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_preview_action_from_template
msgid "Template Preview"
msgstr "Vorlagenvorschau"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__template
msgid "Template Quality Rating Too Low"
msgstr "Vorlage für Qualitätsbewertung zu niedrig"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_attachment_ids
msgid "Template Static Header"
msgstr "Statische Kopfzeile der Vorlage"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__variable_ids
msgid "Template Variables"
msgstr "Vorlagenvariablen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__body
msgid "Template body"
msgstr "Vorlagentext"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Template category is missing"
msgstr "Die Vorlagenkategorie fehlt."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Template variable should be in format {{number}}. Cannot parse "
"\"%(placeholder)s\""
msgstr ""
"Vorlagenvariable sollte im Format {{number}} sein. „%(placeholder)s“ kann "
"nicht geparst werden"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__wa_template_id
#: model:ir.ui.menu,name:whatsapp.whatsapp_template_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Templates"
msgstr "Vorlagen"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates Of %(account_name)s"
msgstr "Vorlagen von %(account_name)s"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Templates created on your"
msgstr "Vorlagen wurden erstellt auf Ihrem"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates synchronized!"
msgstr "Vorlagen synchronisiert!"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Test Credentials"
msgstr "Test-Anmeldedaten"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__text
msgid "Text"
msgstr "Text"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__th
msgid "Thai"
msgstr "Thailändisch"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"The Header Text must either contain no variable or the first one {{1}}."
msgstr ""
"Der Kopfzeilentest muss entweder keine Variable oder die erste {{1}} "
"enthalten."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "The phone number set in \"Buttons\" does not look correct."
msgstr ""
"Die unter „Schaltflächen“ eingestellte Rufnummer sieht nicht korrekt aus."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "The placeholder for a button can only be {{1}}."
msgstr "Der Platzhalter für eine Schaltfläche kan nur {{1}} sein."

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_account_phone_uid_unique
msgid "The same phone number ID already exists"
msgstr "Dieselbe Telefonnummern-ID existiert bereits"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There is no record for preparing demo pdf in model %(model)s"
msgstr ""
"Es gibt keinen Datensatz zur Vorbereitung eines Demo-PDFs im Modell "
"%(model)s"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "There might be other templates that still need the Multi"
msgstr ""
"Es könnte andere Vorlagen geben, die noch die App-übegreifende Nutzung "
"benötigen"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There should be at most 1 variable in the header of the template."
msgstr "Es sollte höchstens 1 Variable in der Kopfzeile der Vorlage geben."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "This join method is not possible for regular channels."
msgstr "Diese Beitretungsmethode ist nicht für reguläre Kanäke möglich."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "To use WhatsApp Configure it first"
msgstr "Um WhatsApp zu verwenden, müssen Sie es erst konfigurieren"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__tr
msgid "Turkish"
msgstr "Türkisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__state
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__message_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__button_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_type
msgid "Type"
msgstr "Typ"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,help:whatsapp.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Art der Serveraktion. Die folgenden Werte sind verfügbar:\n"
"- „Einen Datensatz aktualieren“: Aktualisieren Sie die Werte eines Datensatzes.\n"
"- „Aktivität erstellen“: Erstellen Sie eine Aktivität (Dialog)\n"
"- „E-Mail versenden“: Hinterlassen Sie eine Nachricht, oder Notiz oder senden Sie eine E-Mail (Dialog)\n"
"- „SMS versenden“: Senden Sie eine SMS, protokollieren Sie diese in Dokumenten (SMS), log them on documents (SMS)\n"
"- „Follower hinzufügen/entfernen“: Fügen Sie einem Datensatz Follower hinzu oder entfernen Sie sie (Dialog)\n"
"- „Datensatz erstellen“: Erstellen Sie einen neuen Datensatz mit neuen Werten\n"
"- „Code ausführen“: ein Block Python-Code, der ausgeführt wird\n"
"- „Webhook-Benachrichtigung versenden“: Senden Sie eine Anfrage zur VERÖFFENTLICHUNG an ein externes System, auch bekannt als Webhook\n"
"- „Vorhandene Aktionen ausführen“: Legen Sie eine Aktion fest, die mehrere andere Serveraktionen auslöst\n"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uk
msgid "Ukrainian"
msgstr "Ukrainisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__unknown
msgid "Unknown Error"
msgstr "Unbekannter Fehler"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Unknown error when processing whatsapp request."
msgstr "Unbekannter Fehler bei der Verarbeitung der WhatsApp-Anfrage."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ur
msgid "Urdu"
msgstr "Urdu"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__url_type
msgid "Url Type"
msgstr "URL-Typ"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,help:whatsapp.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Wird verwendet, um den Nachrichtengenerator zu kategorisieren\n"
"„email“: generiert durch eine eingehende E-Mail, z. B. von mailgateway\n"
"„comment“: generiert durch Benutzereingaben, z. B. über Dialog oder Editor\n"
"„email_outgoing“: generiert durch ein Mailing\n"
"„notification“: vom System generiert, z. B. durch Verfolgungsnachrichten\n"
"„auto_comment“: generiert durch einen automatischen Benachrichtigungsmechanismus, z. B. eine Bestätigung\n"
"„user_notification“: für einen bestimmten Empfänger generiert"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_mobile
msgid "User Mobile"
msgstr "Mobiltelefon des Benutzers"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_name
msgid "User Name"
msgstr "Benutzername"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_users_settings
msgid "User Settings"
msgstr "Benutzereinstellungen"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has been opt out of receiving WhatsApp messages"
msgstr "Der Benutzer hat den Empfang von WhatsApp-Nachrichten deaktiviert"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has opted in to receiving WhatsApp messages"
msgstr "Der Benutzer hat den Empfang von WhatsApp-Nachrichten deaktiviert"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"User mobile number required in template but no value set on user profile."
msgstr ""
"Mobiltelefon des Benutzers erforderlich in Vorlage, aber kein Wert im "
"Benutzerprofil eingestellt."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__allowed_user_ids
msgid "Users"
msgstr "Benutzer"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Users to notify is required"
msgstr "Zu benachrichtigende Benutzer sind erforderlich"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__notify_user_ids
msgid ""
"Users to notify when a message is received and there is no template send in "
"last 15 days"
msgstr ""
"Benutzer werden benachrichtigt, wenn eine Nachricht eingegangen ist und in "
"den letzten 15 Tagen keine Vorlage gesendet wurde"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_message__failure_reason
msgid "Usually an error message from Whatsapp"
msgstr "Normalerweise eine Fehlermeldung von Whatsapp"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__utility
msgid "Utility"
msgstr "Nutzwert"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uz
msgid "Uzbek"
msgstr "Usbekisch"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__variable_ids
msgid "Variable"
msgstr "Variable"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__line_type
msgid "Variable location"
msgstr "Standort der Variable"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_variable_name_type_template_unique
msgid "Variable names must be unique for a given template"
msgstr ""
"Namen von Variablen müssen in einer bestimmten Vorlage einzigartig sein"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Variables %(field_names)s do not seem to be valid field path for model "
"%(model_name)s."
msgstr ""
"Die Variablen %(field_names)s scheinen kein gültiger Pfad für das Modell "
"%(model_name)s zu sein."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__video
msgid "Video"
msgstr "Video"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__vi
msgid "Vietnamese"
msgstr "Vietnamesisch"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__url
msgid "Visit Website"
msgstr "Website besuchen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__wa_template_id
msgid "Wa Template"
msgstr "WA-Vorlage"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__webhook_verify_token
msgid "Webhook Verify Token"
msgstr "Webhook-Verifizierungstoken"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__website_url
msgid "Website URL"
msgstr "Website-URL"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.xml:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.xml:0
#: code:addons/whatsapp/static/src/core/common/thread_icon_patch.xml:0
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/public_web/messaging_menu_patch.js:0
#: model:ir.model.fields.selection,name:whatsapp.selection__mail_message__message_type__whatsapp_message
#: model:ir.ui.menu,name:whatsapp.whatsapp_menu_main
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "WhatsApp"
msgstr "WhatsApp"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_actions_server_resend_whatsapp_queue
msgid "WhatsApp : Resend failed Messages"
msgstr "WhatsApp: fehlgeschlagene Nachrichten erneut senden"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_cron_send_whatsapp_queue_ir_actions_server
msgid "WhatsApp : Send In Queue Messages"
msgstr "WhatsApp: Nachrichten in der Warteschlange senden"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "WhatsApp Account"
msgstr "WhatsApp-Konto"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_account_action
#: model:ir.model,name:whatsapp.model_whatsapp_account
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__wa_account_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account"
msgstr "WhatsApp-Business-Konto"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account ID"
msgstr "ID des WhatsApp-Business-Kontos"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_account_menu
msgid "WhatsApp Business Accounts"
msgstr "WhatsApp-Business-Konten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "WhatsApp Category Open"
msgstr "WhatsApp-Kategorie offen"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_partner__wa_channel_count
#: model:ir.model.fields,field_description:whatsapp.field_res_users__wa_channel_count
msgid "WhatsApp Channel Count"
msgstr "Anzahl WhatsApp-Kanäle"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_valid_until
msgid "WhatsApp Channel Valid Until Datetime"
msgstr "WhatsApp-Kanal gültig bis Datum/Uhrzeit"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "WhatsApp Chats"
msgstr "WhatsApp-Chats"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__discuss_channel__channel_type__whatsapp
msgid "WhatsApp Conversation"
msgstr "WhatsApp-Unterhaltung"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "WhatsApp Message"
msgstr "WhatsApp-Nachricht"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__msg_uid
msgid "WhatsApp Message ID"
msgstr "WhatsApp-Nachrichten-ID"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_message_action
#: model:ir.model,name:whatsapp.model_whatsapp_message
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_graph
msgid "WhatsApp Messages"
msgstr "WhatsApp-Nachrichten"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_partner_id
msgid "WhatsApp Partner"
msgstr "WhatsApp-Partner"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_template_action
#: model:ir.model,name:whatsapp.model_whatsapp_template
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_tree
msgid "WhatsApp Template"
msgstr "WhatsApp-Vorlage"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_button
msgid "WhatsApp Template Button"
msgstr "Schaltfläche der WhatsApp-Vorlage"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_template_uid
msgid "WhatsApp Template ID"
msgstr "ID der WhatsApp-Vorlage"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_variable
msgid "WhatsApp Template Variable"
msgstr "WhatsApp-Vorlagenvariable"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/im_status_patch.xml:0
#: code:addons/whatsapp/static/src/discuss/core/common/channel_member_list_patch.xml:0
msgid "WhatsApp User"
msgstr "WhatsApp-Benutzer"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp account is misconfigured or shared."
msgstr "Falsch konfiguriertes oder geteiltes WhatsApp-Konto."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp could not be reached or the query was malformed."
msgstr ""
"Whatsapp konnte nicht erreicht werden oder die Anfrage war fehlerhaft "
"formuliert."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"When using a \"location\" header, there should 4 location variables not "
"%(count)d."
msgstr ""
"Bei der Verwendung der Kopfzeile „Standort“ sollte es 4 Standortvariablen "
"geben, nicht %(count)d."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__phone_invalid
msgid "Wrong Number Format"
msgstr "Falsches Zahlenformat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__yellow
msgid "Yellow"
msgstr "Gelb"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You are not allowed to use %(field)s in phone field, contact your "
"administrator to configure it."
msgstr ""
"Es ist Ihnen leider nicht gestattet, %(field)s im Telefonfeld zu verwenden. "
"Bitte wenden Sie sich an Ihren Administrator, um es zu konfigurieren."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"You are not allowed to use field %(field)s, contact your administrator."
msgstr ""
"Es ist Ihnen leider nicht gestattet, das Feld %(field)s zu verwenden. Bitte "
"wenden Sie sich an Ihren Administrator."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not cancel message which is in queue."
msgstr ""
"Sie können eine Nachricht, die sich in der Warteschlange befindet, nicht "
"abbrechen."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not resend message which is not in failed state."
msgstr ""
"Sie können eine Nachricht, die sich nicht in einem Fehlerstatus befindet, "
"nicht erneut senden."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "You can not select field of %(model)s."
msgstr "Sie können das Feld von %(model)s nicht auswählen."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.js:0
msgid "You can set a maximum of 10 variables."
msgstr "Sie können maximal 10 Variablen festlegen."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid ""
"You can't leave this channel. As you are the owner of this WhatsApp channel,"
" you can only delete it."
msgstr ""
"Sie können diesen Kanal nicht verlassen. Da Sie der Eigentümer dieses "
"WhatsApp-Kanals sind, können Sie ihn nur löschen."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You cannot modify a template model when it is linked to server actions."
msgstr ""
"Sie können ein Vorlagenmodell nicht ändern, wenn es mit Serveraktionen "
"verknüpft ist."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "You may only use one header attachment for each template"
msgstr "Sie können für jede Vorlage nur einen Kopfzeilenanhang verwenden"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Your Template has been rejected."
msgstr "Ihre Vorlage wurde abgelehnt."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zu
msgid "Zulu"
msgstr "Zulu"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "another document"
msgstr "weiteres Dokument"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. \"Acme Inc. Business Account\""
msgstr "z. B. „Acme-Inc.-Buisness-Konto“"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. \"Send Order Document\""
msgstr "z. B. „Auftragsdokument versenden“"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. 00112233445566778899aabbccddeeff"
msgstr "z. B. 00112233445566778899aabbccddeeff"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. ***************"
msgstr "z. B. ***************"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"
msgstr "z. B. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. Invitation for {{1}}"
msgstr "z. B. Einladung für {{1}}"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. https://www.example.com"
msgstr "z. B. http://www.beispiel.com"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "platform then connect it to your Odoo database"
msgstr "und verknüpfen Sie es mit Ihrer Odoo-Datenbank"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "recipients have an invalid phone number and will be skipped."
msgstr "Empfänger haben eine ungültige Telefonnummer und werden übersprungen."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "variable"
msgstr "Variable"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid ""
"will be visible here once they're synced.\n"
"                You can also write new ones from here and submit them for approval, following the"
msgstr ""
"werden hier angezeigt, sobald sie synchronisiert sind.\n"
"                Sie können von hier aus auch neue Nachrichten verfassen und sie zur Genehmigung einreichen, gemäß den"
