# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp
# 
# Translators:
# eriiikgt, 2024
# <PERSON><PERSON><PERSON>ur<PERSON>, 2024
# oscaryuu, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> Consulting <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> Bochaca <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Santiago <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Ó<PERSON><PERSON> Fons<PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <eant<PERSON>@users.noreply.github.com>, 2024
# marc<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Josep Anton Belchi, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "%(create_count)s were created, %(update_count)s were updated"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (còpia)"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(template_name)s [%(account_name)s]"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "'%(field)s' does not seem to be a valid field path on %(model)s"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ", ... (%s Others)"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_kanban
msgid ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "<span class=\"o_stat_text\">Chats</span>"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid ""
"<strong>Invalid number: </strong>\n"
"                            <span>make sure to set a country on the Contact or to specify the country code.</span>"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A new WhatsApp channel is created for this document"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid ""
"A new template was sent on %(record_link)s.<br>Future replies will be "
"transferred to a new chat."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A phone number is required for WhatsApp channels %(channel_names)s"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__token
msgid "Access Token"
msgstr "Token d'accés"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Accessible to all Users"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Account"
msgstr "Compte"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__account
msgid "Account Error"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__account_uid
msgid "Account ID"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__active
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__active
msgid "Active"
msgstr "Actiu"

#. module: whatsapp
#: model:res.groups,name:whatsapp.group_whatsapp_admin
msgid "Administrator"
msgstr "Administrador"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__af
msgid "Afrikaans"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sq
msgid "Albanian"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "All dynamic urls must have a placeholder."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Allow Multi"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__allowed_company_ids
msgid "Allowed Company"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Allowed companies"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_uid
msgid "App ID"
msgstr "ID de l'aplicació"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_secret
msgid "App Secret"
msgstr "App Secreta"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model_id
msgid "Applies to"
msgstr "S'aplica a"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__approved
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Approved"
msgstr "Aprovat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ar
msgid "Arabic"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__attachment_id
msgid "Attachment"
msgstr "Adjunt"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_attachment_count
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "Attachment mimetype is not supported by WhatsApp: %s."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__authentication
msgid "Authentication"
msgstr "Autenticació"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__template_type
msgid ""
"Authentication - One-time passwords that your customers use to authenticate a transaction or login.\n"
"Marketing - Promotions or information about your business, products or services. Or any message that isn't utility or authentication.\n"
"Utility - Messages about a specific transaction, account, order or customer request."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__az
msgid "Azerbaijani"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_base
msgid "Base"
msgstr "Base"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bn
msgid "Bengali"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__blacklisted
msgid "Blacklisted Phone Number"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__body
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__body
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Body"
msgstr "Cos del missatge"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Body variables should start at 1 and not skip any number, missing %d"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__bounced
msgid "Bounced"
msgstr "Rebotat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bg
msgid "Bulgarian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__button_id
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__button
msgid "Button"
msgstr "Botó "

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__name
msgid "Button Text"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_1
msgid "Button Url 1"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_2
msgid "Button Url 2"
msgstr ""

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_button_unique_name_per_template
msgid "Button names must be unique in a given template"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Button variables must be linked to a button."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__button_ids
msgid "Buttons"
msgstr "Botons"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Buttons may only contain one placeholder."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__call_number
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__phone_number
msgid "Call Number"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__callback_url
msgid "Callback URL"
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid ""
"Can't send message as it has been 24 hours since the last message of the "
"User."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
msgid "Cancel"
msgstr "Cancel·la"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Cancel WhatsApp"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__cancel
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ca
msgid "Catalan"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_type
msgid "Category"
msgstr "Categoria"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel_member
msgid "Channel Member"
msgstr "Membre del canal"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Tipus de canal"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"El xat és privat i únic entre 2 persones. El grup és privat entre persones "
"convidades. El canal es pot afegir lliurement (segons la seva configuració)."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_cn
msgid "Chinese (CHN)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_hk
msgid "Chinese (HKG)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_tw
msgid "Chinese (TAI)"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.ir_actions_server_view_form_whatsapp
msgid "Choose a template..."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Close"
msgstr "Tancar"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_configuration_menu
msgid "Configuration"
msgstr "Configuració"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Configure Meta Accounts"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "Configure Templates"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Configure Whatsapp Business Account"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Create Date"
msgstr "Data de creació"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Create an Account on the"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Created On"
msgstr "Creat el"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_date
msgid "Created on"
msgstr "Creat el"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Credentials look good!"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hr
msgid "Croatian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__cs
msgid "Czech"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__da
msgid "Danish"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Default Users"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__deleted
msgid "Deleted"
msgstr "Esborrat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__delivered
msgid "Delivered"
msgstr "Lliurat"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Delivered Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__disabled
msgid "Disabled"
msgstr "Inhabilitat "

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Disallow Multi"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel
msgid "Discussion Channel"
msgstr "Canal de debat"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__document
msgid "Document"
msgstr "Document"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_ids
msgid "Document IDs"
msgstr "Identificació de documents"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_model
msgid "Document Model Name"
msgstr "Nom del document model"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload failed, please retry after sometime."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload session open failed, please retry after sometime."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__draft
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Draft"
msgstr "Esborrany"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_unique_name_account_template
msgid "Duplicate template is not allowed for one Meta account."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nl
msgid "Dutch"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__dynamic
msgid "Dynamic"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Dynamic button variable name must be the same as its respective button's "
"name"
msgstr ""

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_message_unique_msg_uid
msgid "Each whatsapp message should correspond to a single message uuid."
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_thread
msgid "Email Thread"
msgstr "Fil de correus"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/phone_field/phone_field.js:0
msgid "Enable WhatsApp"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en
msgid "English"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_gb
msgid "English (UK)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_us
msgid "English (US)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__error_msg
msgid "Error Message"
msgstr "Missatge d'error"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__et
msgid "Estonian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__error
msgid "Failed"
msgstr "Fallits"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Failed Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_reason
msgid "Failure Reason"
msgstr "Raó de la fallada"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_type
msgid "Failure Type"
msgstr "Tipus de fallada"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_name
msgid "Field"
msgstr "Camp"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__field
msgid "Field of Model"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Field template variables %(var_names)s must be associated with a field."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "File type %(file_type)s not supported for header type %(header_type)s"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fil
msgid "Filipino"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fi
msgid "Finnish"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_follower_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_partner_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__footer_text
msgid "Footer Message"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__free_text
msgid "Free Text"
msgstr "Text Lliure"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_1
msgid "Free Text 1"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_10
msgid "Free Text 10"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_2
msgid "Free Text 2"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_3
msgid "Free Text 3"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_4
msgid "Free Text 4"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_5
msgid "Free Text 5"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_6
msgid "Free Text 6"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_7
msgid "Free Text 7"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_8
msgid "Free Text 8"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_9
msgid "Free Text 9"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__free_text_json
msgid "Free Text Template Parameters"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Free Text template variables must have a demo value."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Free text variable in the header should be {{1}}"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fr
msgid "French"
msgstr "Francès"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ka
msgid "Georgian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__de
msgid "German"
msgstr "Alemany"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__el
msgid "Greek"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__green
msgid "Green"
msgstr "Verd"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels and whatsapp."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__gu
msgid "Gujarati"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_action
msgid "Has Action"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_invalid_button_number
msgid "Has Invalid Button Number"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__has_invalid_number
msgid "Has Invalid Number"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__has_message
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ha
msgid "Hausa"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__header
msgid "Header"
msgstr "Capçalera"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__header_text_1
msgid "Header Free Text"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_type
msgid "Header Type"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document is required"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document or report is required"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__he
msgid "Hebrew"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Hello {{1}}, here is your order with the reference {{2}} ..."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hi
msgid "Hindi"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hu
msgid "Hungarian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__id
msgid "ID"
msgstr "ID"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_recoverable
msgid "Identified Error"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "If checked, the WhatsApp category is open in the discuss sidebar"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__image
msgid "Image"
msgstr "Imatge"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__in_appeal
msgid "In Appeal"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__outgoing
msgid "In Queue"
msgstr "A la cua"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__inbound
msgid "Inbound"
msgstr "Entrada"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__id
msgid "Indonesian"
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "Insert variable"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__invalid_phone_number_count
msgid "Invalid Phone Number Count"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ga
msgid "Irish"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_button_dynamic
msgid "Is Button Dynamic"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_is_follower
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_header_free_text
msgid "Is Header Free Text"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__batch_mode
msgid "Is Multiple Records"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_active
msgid "Is Whatsapp Channel Active"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__it
msgid "Italian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ja
msgid "Japanese"
msgstr "Japonès"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kn
msgid "Kannada"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kk
msgid "Kazakh"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__rw_rw
msgid "Kinyarwanda"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ko
msgid "Korean"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ky_kg
msgid "Kyrgyz (Kyrgyzstan)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__lang_code
msgid "Language"
msgstr "Idioma"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lo
msgid "Lao"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Month"
msgstr "Últim mes"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__last_wa_mail_message_id
msgid "Last WA Partner Mail Message"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Week"
msgstr "Darrera setmana"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Year"
msgstr "Últim any"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lv
msgid "Latvian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__limit_exceeded
msgid "Limit Exceeded"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lt
msgid "Lithuanian"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__location
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__location
msgid "Location"
msgstr "Ubicació"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location Latitude and Longitude %(latitude)s / %(longitude)s is not in "
"proper format."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Location variable should be 'name', 'address', 'latitude' or 'longitude'. "
"Cannot parse '%(placeholder)s'"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location variables should only exist when a \"location\" header is selected."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mk
msgid "Macedonian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mail_message_id
msgid "Mail Message"
msgstr "Missatge de correu"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ms
msgid "Malay"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ml
msgid "Malayalam"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mr
msgid "Marathi"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__marketing
msgid "Marketing"
msgstr "Màrqueting"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 1 Call Number button allowed."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 10 buttons allowed."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 2 URL buttons allowed."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Members"
msgstr "Membres"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_message
msgid "Message"
msgstr "Missatge"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__templates_count
msgid "Message Count"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__preview_whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__preview_whatsapp
msgid "Message Preview"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Message Statistics Of %(template_name)s"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__message_type
msgid "Message Type"
msgstr "Tipus de missatge"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_ids
#: model:ir.ui.menu,name:whatsapp.whatsapp_message_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Messages"
msgstr "Missatges"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__messages_count
msgid "Messages Count"
msgstr "Comptador de missatges"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Meta for Developers"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number_formatted
msgid "Mobile Number Formatted"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Model"
msgstr "Model"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__model
msgid "Model Name"
msgstr "Nom del model"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "Monitor all recent outgoing and incoming messages"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "My Templates"
msgstr "Les meves plantilles"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__name
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Name"
msgstr "Nom"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__network
msgid "Network Error"
msgstr "Error de xarxa"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "No Account Configured yet!"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "No Templates Found!"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "No WhatsApp Messages found"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "No approved WhatsApp Templates are available for this model."
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/web/channel_selector_patch.js:0
msgid "No results found"
msgstr "No s' ha trobat cap resultat"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Non-descript Error"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__none
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__none
msgid "None"
msgstr "Cap"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nb
msgid "Norwegian"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Notifications"
msgstr "Notificacions"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__notify_user_ids
msgid "Notify User"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text
msgid "Number of free text"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text_button
msgid "Number of free text Buttons"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only 10 free text is allowed in body of template"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Only dynamic urls may have a placeholder."
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid "Only one attachment is allowed for each message"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only templates using media header types may have header documents"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_unrecoverable
msgid "Other Technical Error"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__outbound
msgid "Outbound"
msgstr "Sortint"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid "Partner created by incoming WhatsApp message."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__paused
msgid "Paused"
msgstr "En pausa"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Pending"
msgstr "Pendent"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending_deletion
msgid "Pending Deletion"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fa
msgid "Persian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__phone
msgid "Phone"
msgstr "Telèfon"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__phone_field
msgid "Phone Field"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_number
msgid "Phone Number"
msgstr "Número de telèfon"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__phone_uid
msgid "Phone Number ID"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Phone number Id is wrong."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Pick an Account..."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Pick users to notify..."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__name
msgid "Placeholder"
msgstr "Indicador de posició"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr ""
"Si us plau, introdueixi almenys 3 caràcters quan busqui un número de "
"telèfon/mòbil."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pl
msgid "Polish"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__portal_url
msgid "Portal Link"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_br
msgid "Portuguese (BR)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_pt
msgid "Portuguese (POR)"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Preview"
msgstr "Vista prèvia"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Preview WhatsApp"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_preview
msgid "Preview template"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pa
msgid "Punjabi"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__quality
msgid "Quality"
msgstr "Qualitat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__quick_reply
msgid "Quick Reply"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__rating_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__read
msgid "Read"
msgstr "Llegir"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Read Messages"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Reason : %s"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__received
msgid "Received"
msgstr "Rebuda"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Receiving Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__red
msgid "Red"
msgstr "Vermell"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__rejected
msgid "Rejected"
msgstr "Rebutjada"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "Related %(model_name)s: "
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model
msgid "Related Document Model"
msgstr "Model de document relacionat"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__wa_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__wa_message_ids
msgid "Related WhatsApp Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__replied
msgid "Replied"
msgstr "Respost"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__report_id
msgid "Report"
msgstr "Informe"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Reset to draft"
msgstr "Restableix a l'esborrany"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__parent_id
msgid "Response To"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Retry"
msgstr "Reintentar"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ro
msgid "Romanian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ru
msgid "Russian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__demo_value
msgid "Sample Value"
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/web/messaging_menu_patch.xml:0
msgid "Search WhatsApp Channel"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid "See all options"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send Message"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__ir_actions_server__state__whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send WhatsApp"
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.js:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.js:0
msgid "Send WhatsApp Message"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Send and receive message through your WhatsApp Business account."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Sending Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__sent
msgid "Sent"
msgstr "Enviat"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent To"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Sent to"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__sequence
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sr
msgid "Serbian"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_ir_actions_server
msgid "Server Action"
msgstr "Acció del Servidor "

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sk
msgid "Slovak"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sl
msgid "Slovenian"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid ""
"Something went wrong when contacting WhatsApp, please try again later. If "
"this happens frequently, contact support."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es
msgid "Spanish"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_ar
msgid "Spanish (ARG)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_mx
msgid "Spanish (MEX)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_es
msgid "Spanish (SPA)"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__state
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "State"
msgstr "Estat"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__static
msgid "Static"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__status
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Status"
msgstr "Estat"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Submit for Approval"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sw
msgid "Swahili"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sv
msgid "Swedish"
msgstr "Suec"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Sync Template"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Synchronize Templates"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ta
msgid "Tamil"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__te
msgid "Telugu"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Template"
msgstr "Plantilla"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"Template %(template_name)s holds a wrong configuration for 'phone field'\n"
"%(error_msg)s"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_button_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_variable_view_form
msgid "Template Button"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Template Guidelines"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_text
msgid "Template Header Text"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_name
msgid "Template Name"
msgstr "Nom de plantilla"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_preview_action_from_template
msgid "Template Preview"
msgstr "Vista prèvia de la plantilla"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__template
msgid "Template Quality Rating Too Low"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_attachment_ids
msgid "Template Static Header"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__variable_ids
msgid "Template Variables"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__body
msgid "Template body"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Template category is missing"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Template variable should be in format {{number}}. Cannot parse "
"\"%(placeholder)s\""
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__wa_template_id
#: model:ir.ui.menu,name:whatsapp.whatsapp_template_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Templates"
msgstr "Plantilles"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates Of %(account_name)s"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Templates created on your"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates synchronized!"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Test Credentials"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__text
msgid "Text"
msgstr "Text"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__th
msgid "Thai"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"The Header Text must either contain no variable or the first one {{1}}."
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "The phone number set in \"Buttons\" does not look correct."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "The placeholder for a button can only be {{1}}."
msgstr ""

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_account_phone_uid_unique
msgid "The same phone number ID already exists"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There is no record for preparing demo pdf in model %(model)s"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "There might be other templates that still need the Multi"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There should be at most 1 variable in the header of the template."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "This join method is not possible for regular channels."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "To use WhatsApp Configure it first"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__tr
msgid "Turkish"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__state
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__message_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__button_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_type
msgid "Type"
msgstr "Tipus"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,help:whatsapp.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uk
msgid "Ukrainian"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__unknown
msgid "Unknown Error"
msgstr "Error Desconegut "

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Unknown error when processing whatsapp request."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ur
msgid "Urdu"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__url_type
msgid "Url Type"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,help:whatsapp.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_mobile
msgid "User Mobile"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_name
msgid "User Name"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_users_settings
msgid "User Settings"
msgstr "Arranjament d' usuari"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has been opt out of receiving WhatsApp messages"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has opted in to receiving WhatsApp messages"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"User mobile number required in template but no value set on user profile."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__allowed_user_ids
msgid "Users"
msgstr "Usuaris"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Users to notify is required"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__notify_user_ids
msgid ""
"Users to notify when a message is received and there is no template send in "
"last 15 days"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_message__failure_reason
msgid "Usually an error message from Whatsapp"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__utility
msgid "Utility"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uz
msgid "Uzbek"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__variable_ids
msgid "Variable"
msgstr "Variable"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__line_type
msgid "Variable location"
msgstr ""

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_variable_name_type_template_unique
msgid "Variable names must be unique for a given template"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Variables %(field_names)s do not seem to be valid field path for model "
"%(model_name)s."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__video
msgid "Video"
msgstr "Vídeo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__vi
msgid "Vietnamese"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__url
msgid "Visit Website"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__wa_template_id
msgid "Wa Template"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__webhook_verify_token
msgid "Webhook Verify Token"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__website_url
msgid "Website URL"
msgstr "L'URL del lloc web"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.xml:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.xml:0
#: code:addons/whatsapp/static/src/core/common/thread_icon_patch.xml:0
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/public_web/messaging_menu_patch.js:0
#: model:ir.model.fields.selection,name:whatsapp.selection__mail_message__message_type__whatsapp_message
#: model:ir.ui.menu,name:whatsapp.whatsapp_menu_main
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "WhatsApp"
msgstr ""

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_actions_server_resend_whatsapp_queue
msgid "WhatsApp : Resend failed Messages"
msgstr ""

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_cron_send_whatsapp_queue_ir_actions_server
msgid "WhatsApp : Send In Queue Messages"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "WhatsApp Account"
msgstr ""

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_account_action
#: model:ir.model,name:whatsapp.model_whatsapp_account
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__wa_account_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account ID"
msgstr ""

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_account_menu
msgid "WhatsApp Business Accounts"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "WhatsApp Category Open"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_partner__wa_channel_count
#: model:ir.model.fields,field_description:whatsapp.field_res_users__wa_channel_count
msgid "WhatsApp Channel Count"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_valid_until
msgid "WhatsApp Channel Valid Until Datetime"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "WhatsApp Chats"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__discuss_channel__channel_type__whatsapp
msgid "WhatsApp Conversation"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "WhatsApp Message"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__msg_uid
msgid "WhatsApp Message ID"
msgstr ""

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_message_action
#: model:ir.model,name:whatsapp.model_whatsapp_message
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_graph
msgid "WhatsApp Messages"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_partner_id
msgid "WhatsApp Partner"
msgstr ""

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_template_action
#: model:ir.model,name:whatsapp.model_whatsapp_template
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_tree
msgid "WhatsApp Template"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_button
msgid "WhatsApp Template Button"
msgstr ""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_template_uid
msgid "WhatsApp Template ID"
msgstr ""

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_variable
msgid "WhatsApp Template Variable"
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/im_status_patch.xml:0
#: code:addons/whatsapp/static/src/discuss/core/common/channel_member_list_patch.xml:0
msgid "WhatsApp User"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp account is misconfigured or shared."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp could not be reached or the query was malformed."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"When using a \"location\" header, there should 4 location variables not "
"%(count)d."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__phone_invalid
msgid "Wrong Number Format"
msgstr "Format de número incorrecte"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__yellow
msgid "Yellow"
msgstr "Groc"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You are not allowed to use %(field)s in phone field, contact your "
"administrator to configure it."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"You are not allowed to use field %(field)s, contact your administrator."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not cancel message which is in queue."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not resend message which is not in failed state."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "You can not select field of %(model)s."
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.js:0
msgid "You can set a maximum of 10 variables."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid ""
"You can't leave this channel. As you are the owner of this WhatsApp channel,"
" you can only delete it."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You cannot modify a template model when it is linked to server actions."
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "You may only use one header attachment for each template"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Your Template has been rejected."
msgstr ""

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zu
msgid "Zulu"
msgstr ""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "another document"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. \"Acme Inc. Business Account\""
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. \"Send Order Document\""
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. 00112233445566778899aabbccddeeff"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. ***************"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. Invitation for {{1}}"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. https://www.example.com"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "platform then connect it to your Odoo database"
msgstr ""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "recipients have an invalid phone number and will be skipped."
msgstr ""

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "variable"
msgstr ""

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid ""
"will be visible here once they're synced.\n"
"                You can also write new ones from here and submit them for approval, following the"
msgstr ""
