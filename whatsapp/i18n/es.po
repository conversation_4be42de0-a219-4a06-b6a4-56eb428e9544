# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "%(create_count)s were created, %(update_count)s were updated"
msgstr "Se crearon %(create_count)s y se actualizaron %(update_count)s"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(original_name)s (copy)"
msgstr "%(original_name)s (copia)"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "%(template_name)s [%(account_name)s]"
msgstr "%(template_name)s [%(account_name)s]"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "'%(field)s' does not seem to be a valid field path on %(model)s"
msgstr "'%(field)s' no parece ser una ruta de campo válida en %(model)s"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ", ... (%s Others)"
msgstr ", ... (%s otros)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_kanban
msgid ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"
msgstr ""
"<i class=\"fa fa-whatsapp me-1\" title=\"Messages Count\" aria-"
"label=\"Messages Count\"/>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"
msgstr ""
"<span class=\"o-whatsapp-font-11\">{{Location name}}</span><br/>\n"
"                            <span class=\"text-600 o-whatsapp-font-9\">{{Address}}</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "<span class=\"o_stat_text\">Chats</span>"
msgstr "<span class=\"o_stat_text\">Conversaciones</span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"true\">\n"
"                        06:00\n"
"                    </span>"
msgstr ""
"<span class=\"position-absolute bottom-0 end-0 o-whatsapp-font-11 py-1 px-2 text-black-50\" area-hidden=\"True\">\n"
"                        06:00\n"
"                    </span>"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid ""
"<strong>Invalid number: </strong>\n"
"                            <span>make sure to set a country on the Contact or to specify the country code.</span>"
msgstr ""
"<strong>Número no válido: </strong>\n"
"                            <span>asegúrese de establecer un país en el contacto o especificar un código de país.</span>"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A new WhatsApp channel is created for this document"
msgstr "Se crea un nuevo canal de WhatsApp para este documento"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid ""
"A new template was sent on %(record_link)s.<br>Future replies will be "
"transferred to a new chat."
msgstr ""
"Se envió una nueva plantilla en %(record_link)s. <br>Futuras respuestas se "
"transferirán a un nuevo chat."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "A phone number is required for WhatsApp channels %(channel_names)s"
msgstr ""
"Se requiere un número de teléfono para los canales de WhatsApp "
"%(channel_names)s"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__token
msgid "Access Token"
msgstr "Token de acceso"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Accessible to all Users"
msgstr "Accesible para todos los usuarios"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Account"
msgstr "Cuenta"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__account
msgid "Account Error"
msgstr "Error de cuenta"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__account_uid
msgid "Account ID"
msgstr "ID de la cuenta"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__active
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__active
msgid "Active"
msgstr "Activo"

#. module: whatsapp
#: model:res.groups,name:whatsapp.group_whatsapp_admin
msgid "Administrator"
msgstr "Administrador"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__af
msgid "Afrikaans"
msgstr "Afrikáans"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sq
msgid "Albanian"
msgstr "Albanés"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "All dynamic urls must have a placeholder."
msgstr "Todas las urls dinámicas deben tener un marcador de posición."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Allow Multi"
msgstr "Permitir multi"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__allowed_company_ids
msgid "Allowed Company"
msgstr "Empresa permitida"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Allowed companies"
msgstr "Empresas permitidas"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_uid
msgid "App ID"
msgstr "ID de la aplicación"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__app_secret
msgid "App Secret"
msgstr "Secreto de aplicación"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model_id
msgid "Applies to"
msgstr "Aplica a"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__approved
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Approved"
msgstr "Aprobado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ar
msgid "Arabic"
msgstr "Árabe"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Archived"
msgstr "Archivado"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__attachment_id
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_attachment_count
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "Attachment mimetype is not supported by WhatsApp: %s."
msgstr "El tipo de MIME no es compatible con WhatsApp: %s."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__authentication
msgid "Authentication"
msgstr "Autentificación"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__template_type
msgid ""
"Authentication - One-time passwords that your customers use to authenticate a transaction or login.\n"
"Marketing - Promotions or information about your business, products or services. Or any message that isn't utility or authentication.\n"
"Utility - Messages about a specific transaction, account, order or customer request."
msgstr ""
"Autenticación - contraseñas de un solo uso que sus clientes utilizan para autenticar una transacción o iniciar sesión.\n"
"Marketing - promociones o información sobre su empresa, productos o servicios. Cualquier mensaje que no sea de utilidad o autenticación..\n"
"Utilidad - mensajes sobre una transacción, cuenta, orden o solicitud de cliente específica."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__az
msgid "Azerbaijani"
msgstr "Azerí"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_base
msgid "Base"
msgstr "Base"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bn
msgid "Bengali"
msgstr "Bengalí"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__blacklisted
msgid "Blacklisted Phone Number"
msgstr "Número de teléfono en la lista negra"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__body
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__body
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Body"
msgstr "Contenido"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Body variables should start at 1 and not skip any number, missing %d"
msgstr ""
"Las variables del contenido deben empezar por 1 y no saltarse ningún número,"
" falta %d"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__bounced
msgid "Bounced"
msgstr "Rebotado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__bg
msgid "Bulgarian"
msgstr "Búlgaro"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__button_id
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__button
msgid "Button"
msgstr "Botón"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__name
msgid "Button Text"
msgstr "Texto del botón"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_1
msgid "Button Url 1"
msgstr "URL 1 del botón"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__button_dynamic_url_2
msgid "Button Url 2"
msgstr "URL 2 del botón"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_button_unique_name_per_template
msgid "Button names must be unique in a given template"
msgstr ""
"Los nombres de los botones deben ser únicos en una plantilla determinada"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Button variables must be linked to a button."
msgstr "Las variables de los botones deben estar vinculadas a un botón."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__button_ids
msgid "Buttons"
msgstr "Botones"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Buttons may only contain one placeholder."
msgstr "Los botones solo pueden contener un marcador de posición."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__call_number
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__phone_number
msgid "Call Number"
msgstr "Número de llamada"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__callback_url
msgid "Callback URL"
msgstr "URL de retrollamada"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid ""
"Can't send message as it has been 24 hours since the last message of the "
"User."
msgstr ""
"No se puede enviar mensaje porque han pasado 24 horas desde el último "
"mensaje del usuario."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Cancel WhatsApp"
msgstr "Cancelar WhatsApp"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ca
msgid "Catalan"
msgstr "Catalán"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_type
msgid "Category"
msgstr "Categoría"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel_member
msgid "Channel Member"
msgstr "Miembro del canal"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Tipo de canal"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"El chat es privado y único entre 2 personas. El grupo es privado entre las "
"personas invitadas. El acceso al canal es libre (dependiendo de su "
"configuración)."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_cn
msgid "Chinese (CHN)"
msgstr "Chino (CHN)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_hk
msgid "Chinese (HKG)"
msgstr "Chino (HKG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zh_tw
msgid "Chinese (TAI)"
msgstr "Chino (TAI)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.ir_actions_server_view_form_whatsapp
msgid "Choose a template..."
msgstr "Elija una plantilla..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Close"
msgstr "Cerrar"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_configuration_menu
msgid "Configuration"
msgstr "Configuración"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Configure Meta Accounts"
msgstr "Configurar cuentas de Meta"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "Configure Templates"
msgstr "Configurar plantillas"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Configure Whatsapp Business Account"
msgstr "Configurar cuenta de WhatsApp Business"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Create Date"
msgstr "Fecha de creación"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Create an Account on the"
msgstr "Cree una cuenta en"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Created On"
msgstr "Creado el"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__create_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__create_date
msgid "Created on"
msgstr "Creado el"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Credentials look good!"
msgstr "Parece que las credenciales son correctas"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hr
msgid "Croatian"
msgstr "Croata"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__cs
msgid "Czech"
msgstr "Checo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__da
msgid "Danish"
msgstr "Danés"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Default Users"
msgstr "Usuarios por defecto"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__deleted
msgid "Deleted"
msgstr "Eliminado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__delivered
msgid "Delivered"
msgstr "Entregado"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Delivered Messages"
msgstr "Mensajes entregados"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__disabled
msgid "Disabled"
msgstr "Deshabilitado"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Disallow Multi"
msgstr "Deshabilitar Multi"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_discuss_channel
msgid "Discussion Channel"
msgstr "Canal de conversaciones"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__display_name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__document
msgid "Document"
msgstr "Documento"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_ids
msgid "Document IDs"
msgstr "IDs de documentos"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__res_model
msgid "Document Model Name"
msgstr "Nombre del modelo del documento"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload failed, please retry after sometime."
msgstr ""
"Ocurrió un error al subir el documento, vuelva a intentarlo más tarde."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Document upload session open failed, please retry after sometime."
msgstr ""
"Ocurrió un error al abrir la sesión de subir documentos, vuelva a intentarlo"
" más tarde."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__draft
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Draft"
msgstr "Borrador"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_unique_name_account_template
msgid "Duplicate template is not allowed for one Meta account."
msgstr "No se permiten plantillas duplicadas para una cuenta Meta."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nl
msgid "Dutch"
msgstr "Neerlandés"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__dynamic
msgid "Dynamic"
msgstr "Dinámico"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Dynamic button variable name must be the same as its respective button's "
"name"
msgstr ""
"El nombre de la variable del botón dinámico debe ser igual al nombre de su "
"botón."

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_message_unique_msg_uid
msgid "Each whatsapp message should correspond to a single message uuid."
msgstr ""
"Cada mensaje de WhatsApp debe corresponder a un único UUID de mensaje."

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_thread
msgid "Email Thread"
msgstr "Hilo de correo electrónico"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/phone_field/phone_field.js:0
msgid "Enable WhatsApp"
msgstr "Habilitar WhatsApp"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en
msgid "English"
msgstr "Inglés"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_gb
msgid "English (UK)"
msgstr "Inglés (UK)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__en_us
msgid "English (US)"
msgstr "Inglés (US)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__error_msg
msgid "Error Message"
msgstr "Mensaje de error"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__et
msgid "Estonian"
msgstr "Estonio"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__error
msgid "Failed"
msgstr "Con error"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Failed Messages"
msgstr "Mensajes con error"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_reason
msgid "Failure Reason"
msgstr "Razón del error"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__failure_type
msgid "Failure Type"
msgstr "Tipo de error"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_name
msgid "Field"
msgstr "Campo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__field
msgid "Field of Model"
msgstr "Campo del modelo"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Field template variables %(var_names)s must be associated with a field."
msgstr ""
"Las variables %(var_names)s de la plantilla de campo deben estar asociadas "
"con un campo."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "File type %(file_type)s not supported for header type %(header_type)s"
msgstr ""
"El tipo de archivo %(file_type)s no es compatible con el tipo de encabezado "
"%(header_type)s"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fil
msgid "Filipino"
msgstr "Filipino"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fi
msgid "Finnish"
msgstr "Finlandés​"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_follower_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_partner_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__footer_text
msgid "Footer Message"
msgstr "Mensaje de pie de página"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__free_text
msgid "Free Text"
msgstr "Texto libre"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_1
msgid "Free Text 1"
msgstr "Texto libre 1"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_10
msgid "Free Text 10"
msgstr "Texto libre 10"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_2
msgid "Free Text 2"
msgstr "Texto libre 2"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_3
msgid "Free Text 3"
msgstr "Texto libre 3"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_4
msgid "Free Text 4"
msgstr "Texto libre 4"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_5
msgid "Free Text 5"
msgstr "Texto libre 5"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_6
msgid "Free Text 6"
msgstr "Texto libre 6"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_7
msgid "Free Text 7"
msgstr "Texto libre 7"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_8
msgid "Free Text 8"
msgstr "Texto libre 8"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__free_text_9
msgid "Free Text 9"
msgstr "Texto libre 9"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__free_text_json
msgid "Free Text Template Parameters"
msgstr "Parámetros de la plantilla de texto libre"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "Free Text template variables must have a demo value."
msgstr ""
"Las variables de la plantilla de texto libre deben tener un valor de "
"demostración."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Free text variable in the header should be {{1}}"
msgstr "La variable del texto libre en el encabezado debería ser {{1}}"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fr
msgid "French"
msgstr "Francés"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ka
msgid "Georgian"
msgstr "Georgiano"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__de
msgid "German"
msgstr "Alemán"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__el
msgid "Greek"
msgstr "Griego"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__green
msgid "Green"
msgstr "Verde"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels and whatsapp."
msgstr ""
"La autorización y autosuscripción de grupos solo es posible en los canales y"
" en WhatsApp."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__gu
msgid "Gujarati"
msgstr "Guyaratí"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_action
msgid "Has Action"
msgstr "Tiene acción"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_invalid_button_number
msgid "Has Invalid Button Number"
msgstr "Tiene un número de botón no válido"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__has_invalid_number
msgid "Has Invalid Number"
msgstr "Tiene un número no válido "

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__has_message
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ha
msgid "Hausa"
msgstr "Hausa"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__header
msgid "Header"
msgstr "Encabezado"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__header_text_1
msgid "Header Free Text"
msgstr "Texto libre del encabezado"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_type
msgid "Header Type"
msgstr "Tipo de encabezado"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document is required"
msgstr "El documento de encabezado es obligatorio"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Header document or report is required"
msgstr "El documento o informe de encabezado es obligatorio"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__he
msgid "Hebrew"
msgstr "Hebreo"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Hello {{1}}, here is your order with the reference {{2}} ..."
msgstr "Hola {{1}}, esta es su pedido con la referencia {{2}} ..."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hi
msgid "Hindi"
msgstr "Hindi"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__hu
msgid "Hungarian"
msgstr "Húngaro"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__id
msgid "ID"
msgstr "ID"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_recoverable
msgid "Identified Error"
msgstr "Error identificado"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "If checked, the WhatsApp category is open in the discuss sidebar"
msgstr ""
"Si está marcado, la categoría WhatsApp se abrirá en la barra lateral de "
"conversación."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__image
msgid "Image"
msgstr "Imagen"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__in_appeal
msgid "In Appeal"
msgstr "En revisión"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__outgoing
msgid "In Queue"
msgstr "En Cola"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__inbound
msgid "Inbound"
msgstr "Entrante"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__id
msgid "Indonesian"
msgstr "Indonesio"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "Insert variable"
msgstr "Insertar variable"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__invalid_phone_number_count
msgid "Invalid Phone Number Count"
msgstr "Cantidad de números de teléfono no válidos"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ga
msgid "Irish"
msgstr "Irlandés"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_button_dynamic
msgid "Is Button Dynamic"
msgstr "¿Es botón dinámico?"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_is_follower
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__is_header_free_text
msgid "Is Header Free Text"
msgstr "El encabezado es texto libre"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__batch_mode
msgid "Is Multiple Records"
msgstr "Es de registro múltiple"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_active
msgid "Is Whatsapp Channel Active"
msgstr "La conversación de WhatsApp está activa"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__it
msgid "Italian"
msgstr "Italiano"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ja
msgid "Japanese"
msgstr "Japonés"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kn
msgid "Kannada"
msgstr "Canarés"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__kk
msgid "Kazakh"
msgstr "Kazajo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__rw_rw
msgid "Kinyarwanda"
msgstr "Kiñaruanda"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ko
msgid "Korean"
msgstr "Coreano"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ky_kg
msgid "Kyrgyz (Kyrgyzstan)"
msgstr "Kirguís (Kirguistán)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__lang_code
msgid "Language"
msgstr "Idioma"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lo
msgid "Lao"
msgstr "Lao"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Month"
msgstr "Mes anterior"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_uid
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__write_date
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__last_wa_mail_message_id
msgid "Last WA Partner Mail Message"
msgstr "Último mensaje de WhatsApp al contacto"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Week"
msgstr "Semana anterior"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Last Year"
msgstr "Año anterior"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lv
msgid "Latvian"
msgstr "Letón"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__limit_exceeded
msgid "Limit Exceeded"
msgstr "Límite excedido"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__lt
msgid "Lithuanian"
msgstr "Lituano"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__location
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__line_type__location
msgid "Location"
msgstr "Ubicación"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location Latitude and Longitude %(latitude)s / %(longitude)s is not in "
"proper format."
msgstr ""
"La latitud y longitud %(latitude)s/%(longitude)s de la ubicación no están en"
" el formato adecuado."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Location variable should be 'name', 'address', 'latitude' or 'longitude'. "
"Cannot parse '%(placeholder)s'"
msgstr ""
"La variable de ubicación debe ser \"nombre\", \"dirección\", \"latitud\" o "
"\"longitud\". No se puede analizar \"%(placeholder)s\""

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"Location variables should only exist when a \"location\" header is selected."
msgstr ""
"Solo deben existir las variables de ubicación cuando se selecciona una "
"\"ubicación\"."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mk
msgid "Macedonian"
msgstr "Macedonio"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mail_message_id
msgid "Mail Message"
msgstr "Mensaje de correo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ms
msgid "Malay"
msgstr "Malayo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ml
msgid "Malayalam"
msgstr "Malabar"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__mr
msgid "Marathi"
msgstr "Maratí"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__marketing
msgid "Marketing"
msgstr "Marketing"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 1 Call Number button allowed."
msgstr "Se permite un máximo de 1 botón de número de llamada."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 10 buttons allowed."
msgstr "Se permite un máximo de 10 botones."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Maximum 2 URL buttons allowed."
msgstr "Se permite un máximo de 2 botones de URL."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
msgid "Members"
msgstr "Miembros"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_mail_message
msgid "Message"
msgstr "Mensaje"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__templates_count
msgid "Message Count"
msgstr "Número de mensajes"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__preview_whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__preview_whatsapp
msgid "Message Preview"
msgstr "Vista previa del mensaje"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Message Statistics Of %(template_name)s"
msgstr "Estadísticas de mensaje de %(template_name)s"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__message_type
msgid "Message Type"
msgstr "Tipo de mensaje"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_ids
#: model:ir.ui.menu,name:whatsapp.whatsapp_message_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Messages"
msgstr "Mensajes"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__messages_count
msgid "Messages Count"
msgstr "Número de mensajes"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "Meta for Developers"
msgstr "Meta para desarrolladores"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number_formatted
msgid "Mobile Number Formatted"
msgstr "Número de móvil formateado"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Model"
msgstr "Modelo"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__model
msgid "Model Name"
msgstr "Nombre del modelo"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "Monitor all recent outgoing and incoming messages"
msgstr "Supervise todos los mensajes salientes y entrantes recientes"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "My Templates"
msgstr "Mis plantillas"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__name
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__name
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Name"
msgstr "Nombre"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__network
msgid "Network Error"
msgstr "Error de red"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "No Account Configured yet!"
msgstr "Todavía no hay una cuenta configurada"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "No Templates Found!"
msgstr "No se encontró ninguna plantilla"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_message_action
msgid "No WhatsApp Messages found"
msgstr "No se encontraron mensajes de WhatsApp"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid "No approved WhatsApp Templates are available for this model."
msgstr "No hay plantillas de WhatsApp aprobadas disponibles para este modelo."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/web/channel_selector_patch.js:0
msgid "No results found"
msgstr "No se encontraron resultados"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Non-descript Error"
msgstr "Error sin descripción"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__none
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__none
msgid "None"
msgstr "Ninguno"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__nb
msgid "Norwegian"
msgstr "Noruego"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Notifications"
msgstr "Notificaciones"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__notify_user_ids
msgid "Notify User"
msgstr "Notificar usuario"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text
msgid "Number of free text"
msgstr "Número de texto libre"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__number_of_free_text_button
msgid "Number of free text Buttons"
msgstr "Número de botones de texto libre"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_needaction_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__message_has_error_counter
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only 10 free text is allowed in body of template"
msgstr "Solo se permiten 10 textos libres en el cuerpo de una plantilla"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "Only dynamic urls may have a placeholder."
msgstr "Solo las URLs dinámicas deben contar con un marcador de posición."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/composer_patch.js:0
msgid "Only one attachment is allowed for each message"
msgstr "Solo se permite un archivo adjunto por mensaje"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Only templates using media header types may have header documents"
msgstr ""
"Solo las plantillas que utilizan tipos de encabezado multimedia pueden tener"
" documentos de encabezado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__whatsapp_unrecoverable
msgid "Other Technical Error"
msgstr "Otro error técnico"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__message_type__outbound
msgid "Outbound"
msgstr "Saliente"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid "Partner created by incoming WhatsApp message."
msgstr "Contacto creado desde un mensaje entrante de WhatsApp."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__paused
msgid "Paused"
msgstr "En pausa"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Pending"
msgstr "Pendiente"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__pending_deletion
msgid "Pending Deletion"
msgstr "Eliminación pendiente"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__fa
msgid "Persian"
msgstr "Persa"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__phone
msgid "Phone"
msgstr "Teléfono"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__phone_field
msgid "Phone Field"
msgstr "Campo de teléfono"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_number
msgid "Phone Number"
msgstr "Número de teléfono"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__phone_uid
msgid "Phone Number ID"
msgstr "ID del número de teléfono"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "Phone number Id is wrong."
msgstr "El ID del número de teléfono es incorrecto."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Pick an Account..."
msgstr "Elija una cuenta..."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Pick users to notify..."
msgstr "Elija los usuarios a notificar..."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__name
msgid "Placeholder"
msgstr "Marcador de posición"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr ""
"Introduzca al menos 3 caracteres cuando busque un número de teléfono/móvil."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pl
msgid "Polish"
msgstr "Polaco"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__portal_url
msgid "Portal Link"
msgstr "Enlace del portal"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_br
msgid "Portuguese (BR)"
msgstr "Portugués (BR)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pt_pt
msgid "Portuguese (POR)"
msgstr "Portugués (POR)"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Preview"
msgstr "Vista previa"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_preview_view_form
msgid "Preview WhatsApp"
msgstr "Vista previa de WhatsApp"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_preview
msgid "Preview template"
msgstr "Vista previa de la plantilla"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__pa
msgid "Punjabi"
msgstr "Panyabí"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__quality
msgid "Quality"
msgstr "Calidad"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__quick_reply
msgid "Quick Reply"
msgstr "Respuesta rápida"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__rating_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__read
msgid "Read"
msgstr "Leer"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Read Messages"
msgstr "Leer mensajes"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Reason : %s"
msgstr "Motivo: %s"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__received
msgid "Received"
msgstr "Recibido"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Receiving Messages"
msgstr "Recibiendo mensajes"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__red
msgid "Red"
msgstr "Rojo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__status__rejected
msgid "Rejected"
msgstr "Rechazado"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "Related %(model_name)s: "
msgstr "%(model_name)s relacionado: "

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__model
msgid "Related Document Model"
msgstr "Modelo de documento relacionado"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__wa_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__wa_message_ids
msgid "Related WhatsApp Messages"
msgstr "Mensajes de WhatsApp relacionados"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__replied
msgid "Replied"
msgstr "Respondido"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__report_id
msgid "Report"
msgstr "Informe"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Reset to draft"
msgstr "Restablecer a borrador"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__parent_id
msgid "Response To"
msgstr "Respuesta a "

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Retry"
msgstr "Volver a intentar"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ro
msgid "Romanian"
msgstr "Rumano"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ru
msgid "Russian"
msgstr "Ruso"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__message_has_sms_error
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__demo_value
msgid "Sample Value"
msgstr "Valor de ejemplo"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/web/messaging_menu_patch.xml:0
msgid "Search WhatsApp Channel"
msgstr "Buscar canal de WhatsApp"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.template_message_preview
msgid "See all options"
msgstr "Ver todas las opciones"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send Message"
msgstr "Enviar mensaje"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__ir_actions_server__state__whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "Send WhatsApp"
msgstr "Enviar WhatsApp"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.js:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.js:0
msgid "Send WhatsApp Message"
msgstr "Enviar mensaje de WhatsApp"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr "Asistente de envío de WhatsApp"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "Send and receive message through your WhatsApp Business account."
msgstr "Envíe y reciba mensajes desde su cuenta de WhatsApp Business."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Sending Messages"
msgstr "Enviando mensajes"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__state__sent
msgid "Sent"
msgstr "Enviado"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent Messages"
msgstr "Mensajes enviados"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__mobile_number
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Sent To"
msgstr "Enviado a"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_tree
msgid "Sent to"
msgstr "Enviado a"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__sequence
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sr
msgid "Serbian"
msgstr "Serbio"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_ir_actions_server
msgid "Server Action"
msgstr "Acción de servidor"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sk
msgid "Slovak"
msgstr "Eslovaco"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sl
msgid "Slovenian"
msgstr "Esloveno"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid ""
"Something went wrong when contacting WhatsApp, please try again later. If "
"this happens frequently, contact support."
msgstr ""
"Algo salió mal al ponerse en contacto con WhatsApp, inténtelo más tarde. Si "
"esto vuelve a ocurrir, póngase en contacto con el servicio de asistencia."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es
msgid "Spanish"
msgstr "Español"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_ar
msgid "Spanish (ARG)"
msgstr "Español (ARG)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_mx
msgid "Spanish (MEX)"
msgstr "Español (MEX)"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__es_es
msgid "Spanish (SPA)"
msgstr "Español (SPA)"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__state
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "State"
msgstr "Estado"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__url_type__static
msgid "Static"
msgstr "Estático"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__status
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Status"
msgstr "Estado"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Submit for Approval"
msgstr "Enviar para aprobación"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sw
msgid "Swahili"
msgstr "Suajili"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__sv
msgid "Swedish"
msgstr "Sueco"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "Sync Template"
msgstr "Sincronizar plantilla"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Synchronize Templates"
msgstr "Sincronizar plantillas"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ta
msgid "Tamil"
msgstr "Tamil"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__te
msgid "Telugu"
msgstr "Télugu"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_composer__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_search
msgid "Template"
msgstr "Plantilla"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"Template %(template_name)s holds a wrong configuration for 'phone field'\n"
"%(error_msg)s"
msgstr ""
"La plantilla %(template_name)s no tiene la configuración correcta en el 'campo de teléfono'\n"
"%(error_msg)s"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_button_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_variable_view_form
msgid "Template Button"
msgstr "Botón de plantilla"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Template Guidelines"
msgstr "Pautas para las plantillas"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_text
msgid "Template Header Text"
msgstr "Texto del encabezado de la plantilla"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__template_name
msgid "Template Name"
msgstr "Nombre de la plantilla"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_preview_action_from_template
msgid "Template Preview"
msgstr "Vista previa de la plantilla"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__template
msgid "Template Quality Rating Too Low"
msgstr "La calificación de calidad de la plantilla es demasiado baja"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__header_attachment_ids
msgid "Template Static Header"
msgstr "Encabezado estático de plantilla"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__variable_ids
msgid "Template Variables"
msgstr "Variables de plantilla"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__body
msgid "Template body"
msgstr "Cuerpo de la plantilla"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "Template category is missing"
msgstr "Falta la categoría de la plantilla"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Template variable should be in format {{number}}. Cannot parse "
"\"%(placeholder)s\""
msgstr ""
"La variable de la plantilla debe estar en formato {{number}}. No se pudo "
"interpretar \"%(placeholder)s\""

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_preview__wa_template_id
#: model:ir.ui.menu,name:whatsapp.whatsapp_template_menu
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_search
msgid "Templates"
msgstr "Plantillas"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates Of %(account_name)s"
msgstr "Plantillas de %(account_name)s"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "Templates created on your"
msgstr "Las plantillas creadas en su"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Templates synchronized!"
msgstr "Plantillas sincronizadas"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "Test Credentials"
msgstr "Probar credenciales"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__text
msgid "Text"
msgstr "Texto"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__th
msgid "Thai"
msgstr "Tailandés"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"The Header Text must either contain no variable or the first one {{1}}."
msgstr ""
"El texto del encabezado no debe incluir ninguna variable o debe ser {{1}}."

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "The phone number set in \"Buttons\" does not look correct."
msgstr "El número de teléfono configurado en \"Botones\" no parece correcto."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_button.py:0
msgid "The placeholder for a button can only be {{1}}."
msgstr "El marcador de posición de un botón solo puede ser {{1}}."

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_account_phone_uid_unique
msgid "The same phone number ID already exists"
msgstr "Ya existe el mismo ID de número de teléfono"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There is no record for preparing demo pdf in model %(model)s"
msgstr ""
"No hay ningún registro para preparar el pdf de demostración en el modelo "
"%(model)s"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "There might be other templates that still need the Multi"
msgstr "Puede que haya otras plantillas que todavía necesiten el Multi"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "There should be at most 1 variable in the header of the template."
msgstr "Debe haber como máximo 1 variable en el encabezado de la plantilla."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid "This join method is not possible for regular channels."
msgstr "Este método para unirse no es posible para los canales regulares."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_api.py:0
msgid "To use WhatsApp Configure it first"
msgstr "Si desea utilizar WhatsApp, primero debe configurarlo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__tr
msgid "Turkish"
msgstr "Turco"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__state
#: model:ir.model.fields,field_description:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,field_description:whatsapp.field_mail_message__message_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__button_type
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__field_type
msgid "Type"
msgstr "Tipo"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_ir_actions_server__state
#: model:ir.model.fields,help:whatsapp.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Tipo de acción de servidor. Los siguientes valores están disponibles:\n"
"- 'Actualizar un registro': actualizar los valores de un registro\n"
"- 'Crear actividad': crear una actividad (conversaciones)\n"
"- 'Enviar correo electrónico': publicar un mensaje, una nota o enviar un correo electrónico (Conversaciones)\n"
"- 'Enviar SMS': enviar SMS y registrarlos en los documentos (SMS)- 'Añadir o eliminar seguidores': añadir o eliminar seguidores de un registro (Conversaciones)\n"
"- 'Crear registro': crear un nuevo registro con nuevos valores\n"
"- 'Ejecutar código': un bloque de código Python que se ejecutará\n"
"- 'Enviar notificación webhook': enviar una solicitud POST a un sistema externo, también conocido como Webhook\n"
"- 'Ejecutar acciones existentes': definir una acción que activa otras acciones de servidor\n"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uk
msgid "Ukrainian"
msgstr "Ucraniano"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__unknown
msgid "Unknown Error"
msgstr "Error desconocido"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Unknown error when processing whatsapp request."
msgstr "Error desconocido al procesar la solicitud de whatsapp."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__ur
msgid "Urdu"
msgstr "Urdu"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__url_type
msgid "Url Type"
msgstr "Tipo de URL"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_mail_mail__message_type
#: model:ir.model.fields,help:whatsapp.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Utilizado para categorizar el generador de mensajes\n"
"'email': generado por un correo electrónico entrante, por ejemplo, mailgateway\n"
"'comment': generado por la entrada del usuario, por ejemplo, a través de conversaciones o del compositor\n"
"'email_outgoing': generado por un envío de correo\n"
"'notification': generado por el sistema, por ejemplo, mensajes de seguimiento\n"
"'auto_comment': generado por un mecanismo de notificación automatizado, por ejemplo una confirmación de recibo\n"
"'user_notification': generado para un destinatario específico"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_mobile
msgid "User Mobile"
msgstr "Móvil de usuario"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_variable__field_type__user_name
msgid "User Name"
msgstr "Nombres de usuario"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_res_users_settings
msgid "User Settings"
msgstr "Ajustes de usuario"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has been opt out of receiving WhatsApp messages"
msgstr "El usuario ha sido excluido de la recepción de mensajes por WhatsApp"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "User has opted in to receiving WhatsApp messages"
msgstr "El usuario ha aceptado recibir mensajes por WhatsApp"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/wizard/whatsapp_composer.py:0
msgid ""
"User mobile number required in template but no value set on user profile."
msgstr ""
"El número de móvil del usuario es necesario en la plantilla pero no "
"estableció ningún valor en el perfil del usuario."

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__allowed_user_ids
msgid "Users"
msgstr "Usuarios"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_account.py:0
msgid "Users to notify is required"
msgstr "Es necesario notificar a los usuarios"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__notify_user_ids
msgid ""
"Users to notify when a message is received and there is no template send in "
"last 15 days"
msgstr ""
"Los usuarios recibirán una notificación cuando se reciba un mensaje y no se "
"haya enviado ninguna plantilla en los últimos 15 días."

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_message__failure_reason
msgid "Usually an error message from Whatsapp"
msgstr "Suele ser un mensaje de error de Whatsapp"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__template_type__utility
msgid "Utility"
msgstr "Utilidad"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__uz
msgid "Uzbek"
msgstr "Uzbeko"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__variable_ids
msgid "Variable"
msgstr "Variable"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__line_type
msgid "Variable location"
msgstr "Ubicación de la variable"

#. module: whatsapp
#: model:ir.model.constraint,message:whatsapp.constraint_whatsapp_template_variable_name_type_template_unique
msgid "Variable names must be unique for a given template"
msgstr ""
"Los nombres de las variables deben ser únicos para una plantilla específica."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"Variables %(field_names)s do not seem to be valid field path for model "
"%(model_name)s."
msgstr ""
"Las variables %(field_names)s no parecen ser una ruta de campo válida para "
"el modelo %(model_name)s."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__header_type__video
msgid "Video"
msgstr "Vídeo"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__vi
msgid "Vietnamese"
msgstr "Vietnamita"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template_button__button_type__url
msgid "Visit Website"
msgstr "Visitar sitio web"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_variable__wa_template_id
msgid "Wa Template"
msgstr "Plantilla de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__webhook_verify_token
msgid "Webhook Verify Token"
msgstr "Token de verificación de webhook"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template_button__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: whatsapp
#: model:ir.model.fields,help:whatsapp.field_whatsapp_account__website_message_ids
#: model:ir.model.fields,help:whatsapp.field_whatsapp_template__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/chatter/web/chatter_patch.xml:0
#: code:addons/whatsapp/static/src/components/whatsapp_button/whatsapp_button.xml:0
#: code:addons/whatsapp/static/src/core/common/thread_icon_patch.xml:0
#: code:addons/whatsapp/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/whatsapp/static/src/core/public_web/messaging_menu_patch.js:0
#: model:ir.model.fields.selection,name:whatsapp.selection__mail_message__message_type__whatsapp_message
#: model:ir.ui.menu,name:whatsapp.whatsapp_menu_main
#: model_terms:ir.ui.view,arch_db:whatsapp.res_config_settings_view_form
msgid "WhatsApp"
msgstr "WhatsApp"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_actions_server_resend_whatsapp_queue
msgid "WhatsApp : Resend failed Messages"
msgstr "WhatsApp: reenviar mensajes con error"

#. module: whatsapp
#: model:ir.actions.server,name:whatsapp.ir_cron_send_whatsapp_queue_ir_actions_server
msgid "WhatsApp : Send In Queue Messages"
msgstr "WhatsApp: enviar mensajes en cola"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid "WhatsApp Account"
msgstr "Cuenta de WhatsApp"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_account_action
#: model:ir.model,name:whatsapp.model_whatsapp_account
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__wa_account_id
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__wa_account_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account"
msgstr "Cuenta de WhatsApp Business"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "WhatsApp Business Account ID"
msgstr "ID de la cuenta de WhatsApp Business"

#. module: whatsapp
#: model:ir.ui.menu,name:whatsapp.whatsapp_account_menu
msgid "WhatsApp Business Accounts"
msgstr "Cuentas de WhatsApp Business"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_users_settings__is_discuss_sidebar_category_whatsapp_open
msgid "WhatsApp Category Open"
msgstr "Categoría abierta de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_res_partner__wa_channel_count
#: model:ir.model.fields,field_description:whatsapp.field_res_users__wa_channel_count
msgid "WhatsApp Channel Count"
msgstr "Número de canales de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_channel_valid_until
msgid "WhatsApp Channel Valid Until Datetime"
msgstr "El canal de WhatsApp será válido hasta esta fecha y hora"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:whatsapp.discuss_channel_view_list_whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.view_partner_form
msgid "WhatsApp Chats"
msgstr "Conversaciones de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__discuss_channel__channel_type__whatsapp
msgid "WhatsApp Conversation"
msgstr "Conversación de WhatsApp"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "WhatsApp Message"
msgstr "Mensaje de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_message__msg_uid
msgid "WhatsApp Message ID"
msgstr "ID del mensaje de WhatsApp"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_message_action
#: model:ir.model,name:whatsapp.model_whatsapp_message
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_message_view_graph
msgid "WhatsApp Messages"
msgstr "Mensajes de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_discuss_channel__whatsapp_partner_id
msgid "WhatsApp Partner"
msgstr "Contacto de WhatsApp"

#. module: whatsapp
#: model:ir.actions.act_window,name:whatsapp.whatsapp_template_action
#: model:ir.model,name:whatsapp.model_whatsapp_template
#: model:ir.model.fields,field_description:whatsapp.field_ir_actions_server__wa_template_id
#: model:ir.model.fields,field_description:whatsapp.field_ir_cron__wa_template_id
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_tree
msgid "WhatsApp Template"
msgstr "Plantilla de WhatsApp"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_button
msgid "WhatsApp Template Button"
msgstr "Botón de la plantilla de WhatsApp"

#. module: whatsapp
#: model:ir.model.fields,field_description:whatsapp.field_whatsapp_template__wa_template_uid
msgid "WhatsApp Template ID"
msgstr "ID de la plantilla de WhatsApp"

#. module: whatsapp
#: model:ir.model,name:whatsapp.model_whatsapp_template_variable
msgid "WhatsApp Template Variable"
msgstr "Variable de la plantilla de WhatsApp"

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/core/common/im_status_patch.xml:0
#: code:addons/whatsapp/static/src/discuss/core/common/channel_member_list_patch.xml:0
msgid "WhatsApp User"
msgstr "Usuario de WhatsApp"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp account is misconfigured or shared."
msgstr "La cuenta de Whatsapp está mal configurada o es compartida."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/tools/whatsapp_exception.py:0
msgid "Whatsapp could not be reached or the query was malformed."
msgstr ""
"No se ha podido contactar con WhatsApp o la consulta estaba mal formulada."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"When using a \"location\" header, there should 4 location variables not "
"%(count)d."
msgstr ""
"Al utilizar un encabezado de ubicación debe haber 4 variables, no %(count)d."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_message__failure_type__phone_invalid
msgid "Wrong Number Format"
msgstr "Formato de número incorrecto"

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__quality__yellow
msgid "Yellow"
msgstr "Amarillo"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You are not allowed to use %(field)s in phone field, contact your "
"administrator to configure it."
msgstr ""
"No está permitido utilizar %(field)s en el campo de teléfono, póngase en "
"contacto con su administrador para configurarlo."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid ""
"You are not allowed to use field %(field)s, contact your administrator."
msgstr ""
"No está permitido utilizar el campo %(field)s, contacte a su administrador."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not cancel message which is in queue."
msgstr "No puede cancelar un mensaje que está en cola."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "You can not resend message which is not in failed state."
msgstr "No puede reenviar un mensaje que no se encuentre en estado fallido."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
#: code:addons/whatsapp/models/whatsapp_template_variable.py:0
msgid "You can not select field of %(model)s."
msgstr "No es posible seleccionar el campo de %(model)s."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.js:0
msgid "You can set a maximum of 10 variables."
msgstr "Puede configurar un máximo de 10 variables."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/discuss_channel.py:0
msgid ""
"You can't leave this channel. As you are the owner of this WhatsApp channel,"
" you can only delete it."
msgstr ""
"No puede abandonar este canal. Como usted es el propietario de este canal de"
" WhatsApp, solo puede eliminarlo."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid ""
"You cannot modify a template model when it is linked to server actions."
msgstr ""
"No puede modificar un modelo de plantilla cuando está vinculado a acciones "
"del servidor."

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_template.py:0
msgid "You may only use one header attachment for each template"
msgstr ""
"Solo puede utilizar un archivo adjunto como encabezado para cada plantilla"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/controller/main.py:0
msgid "Your Template has been rejected."
msgstr "Se ha rechazado su plantilla."

#. module: whatsapp
#: model:ir.model.fields.selection,name:whatsapp.selection__whatsapp_template__lang_code__zu
msgid "Zulu"
msgstr "Zulú"

#. module: whatsapp
#. odoo-python
#: code:addons/whatsapp/models/whatsapp_message.py:0
msgid "another document"
msgstr "otro documento"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. \"Acme Inc. Business Account\""
msgstr "p. ej. \"Cuenta empresarial de Acme Inc.\""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. \"Send Order Document\""
msgstr "p. ej. \"Enviar documento del pedido\""

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. 00112233445566778899aabbccddeeff"
msgstr "p. ej. 00112233445566778899aabbccddeeff"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. ***************"
msgstr "p. ej. ***************"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_account_view_form
msgid "e.g. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"
msgstr "p. ej. EGTRWHRTHETHWRBTEJETHGQEGWRHWR"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. Invitation for {{1}}"
msgstr "p. ej. invitación para {{1}}"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_template_view_form
msgid "e.g. https://www.example.com"
msgstr "p. ej. https://www.ejemplo.com"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_account_action
msgid "platform then connect it to your Odoo database"
msgstr "y luego conéctela a su base de datos de Odoo"

#. module: whatsapp
#: model_terms:ir.ui.view,arch_db:whatsapp.whatsapp_composer_view_form
msgid "recipients have an invalid phone number and will be skipped."
msgstr ""
"de los destinatarios tienen un número de teléfono no válido y serán "
"omitidos."

#. module: whatsapp
#. odoo-javascript
#: code:addons/whatsapp/static/src/components/whatsapp_variables_text_field/whatsapp_variables_text_field.xml:0
msgid "variable"
msgstr "variable"

#. module: whatsapp
#: model_terms:ir.actions.act_window,help:whatsapp.whatsapp_template_action
msgid ""
"will be visible here once they're synced.\n"
"                You can also write new ones from here and submit them for approval, following the"
msgstr ""
"aparecerán aquí cuando hayan sido sincronizadas.\n"
"                También puede escribir nuevas desde aquí y enviarlas para su aprobación, siguiendo los"
