<?xml version="1.0" encoding="utf-8" ?>
<templates>
    <t t-name="web_grid.Renderer">
        <div class="o_grid_renderer position-relative z-0" t-ref="renderer">
            <div class="o_grid_grid d-grid gap-0 border-bottom bg-100"
                 t-attf-style="grid-template-rows: {{ isMobile ? 'auto' : gridTemplateRows }}; grid-template-columns: {{ gridTemplateColumns }}"
                 t-on-mouseover.stop="onMouseOver"
                 t-on-mouseout.stop="onMouseOut"
            >
                <t t-call="web_grid.Header"/>
                <t t-foreach="virtualRows" t-as="row" t-key="row.id">
                    <t t-if="row.isSection">
                        <t t-call="web_grid.Section"/>
                    </t>
                    <t t-else="">
                        <t t-call="web_grid.Row"/>
                        <t t-if="displayAddLine">
                            <t t-call="web_grid.AddLine"/>
                        </t>
                    </t>
                </t>
                <t t-if="props.createInline and !props.model.hasData()" t-call="web_grid.AddLine"/>
                <t t-call="web_grid.Footer"/>
                <t t-if="props.options.hasBarChartTotal" t-call="web_grid.barChart"/>
                <GridComponent
                    t-props="hoveredCellProps"
                />
                <GridComponent
                    t-props="editCellProps"
                />
            </div>
        </div>
    </t>
    <t t-name="web_grid.Header">
        <div class="o_grid_column_title o_grid_navigation_wrap position-md-sticky top-0 start-0 d-flex align-items-center gap-2 px-3 border-bottom bg-100 overflow-visible"
             t-attf-style="grid-row: {{rowsGap}}; grid-column: {{columnsGap}};">
            <t t-call="web_grid.NavigationButtons"/>
        </div>
        <div t-foreach="props.columns"
             t-as="column"
             t-key="column.id"
             class="o_grid_column_title position-relative position-md-sticky top-0 d-flex align-items-center justify-content-center px-3 py-2 border-bottom text-bg-100"
             t-att-class="{
                'fw-bolder': column.isToday,
                'fw-bold' : !column.isToday,
                'text-opacity-25' : getUnavailableClass(column) === 'o_grid_unavailable'
             }"
             t-att-data-grid-row="rowsGap"
             t-att-data-grid-column="column_index + 1 + columnsGap"
             t-attf-style="grid-row: {{rowsGap}}; grid-column: {{column_index + 1 + columnsGap}};"
        >
            <div class="position-absolute top-0 start-0 w-100 h-100"
                 t-att-class="{
                    'o_grid_cell_overlay_today bg-info': column.isToday,
                    'o_grid_cell_overlay bg-700' : !column.isToday,
                 }"
                 t-attf-class="{{getUnavailableClass(column)}}"/>
            <span class="z-1 text-center" t-out="column.title"/>
        </div>
        <div t-if="!props.options.hideLineTotal"
             class="o_grid_column_title o_grid_row_total position-md-sticky top-0 end-0 d-flex align-items-center justify-content-center text-bg-200 px-3 py-2 border-bottom fw-bold text-center"
             t-attf-style="grid-row: {{rowsGap}}; grid-column: {{props.columns.length + 1 + columnsGap}};"
        >
            <span class="z-1" t-out="measureLabel"/>
        </div>
    </t>
    <t t-name="web_grid.Section">
        <t t-set="section" t-value="row"/>
        <t t-set="rowPosition" t-value="getRowPosition(section)"/>
        <t name="section" t-if="!section.isFake and (props.sections.length &gt; 1 or section.value)">
            <div class="o_grid_section o_grid_section_title o_grid_highlightable d-flex align-items-center ps-3 border-top bg-200 fw-bold"
                 t-att-class="{
                    'position-md-sticky start-0': !props.model.useSampleModel,
                 }"
                 t-att-data-grid-row="rowPosition"
                 t-att-data-grid-column="columnsGap"
                 t-attf-style="grid-row: {{rowPosition}}; grid-column: {{columnsGap}};"
            >
            <div class="o_grid_cell_overlay position-absolute top-0 start-0 w-100 h-100 bg-700"/>
                <GridComponent
                    classNames="'z-1 text-truncate'"
                    name="props.sectionField.name"
                    model="props.model"
                    row="section"
                    t-props="getFieldAdditionalProps(props.sectionField.name)"
                />
            </div>
            <div t-foreach="props.columns"
                t-as="column"
                t-key="column.id"
                t-attf-class="o_grid_section o_grid_highlightable position-relative d-flex align-items-center justify-content-center fw-bold {{ getCellColorClass(column) }}"
                t-att-class="getSectionColumnsClasses(column, section)"
                t-att-data-row="section.id"
                t-att-data-column="column.id"
                t-att-data-grid-row="rowPosition"
                t-att-data-grid-column="column_index + 1 + columnsGap"
                t-attf-style="grid-row: {{rowPosition}}; grid-column: {{column_index + 1 + columnsGap}};"
            >
                <div class="position-absolute top-0 start-0 w-100 h-100"
                    t-att-class="{
                       'o_grid_cell_overlay_today bg-info bg-opacity-25' : column.isToday,
                       'o_grid_cell_overlay bg-700' : !column.isToday,
                    }"
                    t-attf-class="{{getUnavailableClass(column)}}"
                />
                <div class="o_grid_cell_readonly position-relative d-flex justify-content-center align-items-center w-100 h-100">
                    <span class="z-1" t-att-class="getSectionCellsClasses(column, section)"
                          t-out="formatValue(section.cells[column.id].value)"
                    />
                </div>
            </div>
            <t t-set="grandTotal" t-value="section.getGrandTotal(props.state.isWeekendVisible)" />
            <div t-if="!props.options.hideLineTotal"
                 class="o_grid_section o_grid_row_total o_grid_highlightable position-relative position-md-sticky end-0 z-1 d-flex align-items-center justify-content-center px-3 py-1 border-top fw-bold"
                 t-att-class="getSectionTotalRowClass(section, grandTotal)"
                 t-att-data-grid-row="rowPosition"
                 t-att-data-grid-column="props.columns.length + 1 + columnsGap"
                 t-attf-style="grid-row: {{rowPosition}}; grid-column: {{props.columns.length + 1 + columnsGap}};"
            >
                <div class="o_grid_cell_overlay_total position-absolute top-0 start-0 w-100 h-100 bg-900"/>
                <div class="position-relative d-flex align-items-center">
                    <span t-out="formatValue(grandTotal)"/>
                </div>
            </div>
        </t>
    </t>
    <t t-name="web_grid.Row">
        <t t-set="rowPosition" t-value="getRowPosition(row)"/>
        <t t-set="isEven" t-value="rowPosition % 2 !== 0"/>
        <div name="row"
             class="o_grid_row o_grid_row_title o_grid_highlightable position-relative d-flex flex-column flex-md-row justify-content-center justify-content-md-start align-items-md-center px-3 py-1"
             t-att-class="{
                'position-md-sticky start-0': !props.model.useSampleModel,
                'bg-view': isEven,
                'bg-100': !isEven,
                'fst-italic': row.isAdditionalRow,
                'text-opacity-25' : getUnavailableClass(row) === 'o_grid_unavailable'
             }"
             t-att-data-grid-row="rowPosition"
             t-att-data-grid-column="columnsGap"
             t-attf-style="grid-row: {{rowPosition}}; grid-column: {{columnsGap}};"
        >
            <div class="o_grid_cell_overlay position-absolute top-0 start-0 w-100 h-100 bg-700"/>
            <t t-set="rowFields" t-value="props.model.rowFields.map(field => field.name)"/>
            <t t-foreach="props.rowFields" t-as="rowField" t-key="rowField_index">
                <t t-set="fieldName" t-value="rowField.name"/>
                <GridComponent
                    t-if="row.initialRecordValues[fieldName] or rowField_index === 0"
                    classNames="`d-flex z-1 text-truncate o_grid_field_${fieldName.replace('_id','')}${isMobile ? '' : ' text-nowrap'}${row.isAdditionalRow ? ' pe-1' : ''}`"
                    name="fieldName"
                    value="row.initialRecordValues[fieldName]"
                    model="props.model"
                    row="row"
                    t-props="getFieldAdditionalProps(fieldName)"
                />
                <span t-if="!rowField_last and row.initialRecordValues[props.rowFields[rowField_index + 1].name]"
                      class="o_grid_row_data_separator d-none d-md-inline px-2 text-300"
                >
                |
                </span>
            </t>
        </div>
        <div t-foreach="props.columns"
             t-as="column"
             t-key="column.id"
             class="o_grid_row o_grid_highlightable position-relative d-flex align-items-center justify-content-center"
             t-att-class="getCellsClasses(column, row, section, isEven)"
             t-att-data-row="row.id"
             t-att-data-column="column.id"
             t-att-data-grid-row="rowPosition"
             t-att-data-grid-column="column_index + 1 + columnsGap"
             t-attf-style="grid-row: {{rowPosition}}; grid-column: {{column_index + 1 + columnsGap}};"
             t-on-click.prevent.synthetic="onCellClick"
        >
            <div class="position-absolute top-0 start-0 w-100 h-100"
                 t-att-class="{
                    'o_grid_cell_overlay_today bg-info bg-opacity-25' : column.isToday,
                    'o_grid_cell_overlay bg-700' : !column.isToday,
                 }"
                 t-attf-class="{{getUnavailableClass(column)}}"
            />
            <div class="o_grid_cell_readonly position-relative d-flex justify-content-center align-items-center w-100 h-100">
                <span t-att-class="{
                          'text-900 text-opacity-25': row.cells[column.id].value === 0,
                      }"
                      t-esc="formatValue(row.cells[column.id].value)"
                />
            </div>
        </div>
        <t t-set="grandTotal" t-value="row.getGrandTotal(props.state.isWeekendVisible)" />
        <div t-if="!props.options.hideLineTotal"
             class="o_grid_row o_grid_row_total o_grid_highlightable position-relative position-md-sticky end-0 d-flex align-items-center justify-content-center px-3 py-1"
             t-att-class="{
                'fst-italic': row.isAdditionalRow,
                'text-bg-200': grandTotal &gt;= 0,
                'bg-danger text-bg-danger': grandTotal lt 0,
                'text-opacity-50': grandTotal === 0
             }"
             t-att-data-grid-row="rowPosition"
             t-att-data-grid-column="props.columns.length + 1 + columnsGap"
             t-attf-style="grid-row: {{rowPosition}}; grid-column: {{props.columns.length + 1 + columnsGap}};"
        >
            <div class="o_grid_cell_overlay_total position-absolute top-0 start-0 w-100 h-100 bg-900"/>
            <span class="z-1" t-out="formatValue(grandTotal)"/>

        </div>
    </t>
    <t t-name="web_grid.AddLine">
        <t t-set="rowPosition" t-value="getRowPosition(row or undefined, true)"/>
        <t t-set="isEven" t-value="rowPosition % 2 !== 0"/>
        <div t-if="props.createInline"
             class="o_grid_row o_grid_add_line o_grid_add_line_first_cell position-md-sticky start-0 d-flex align-items-center z-1 ps-3"
             t-att-class="{
                'bg-view': isEven,
                'bg-100': !isEven,
             }"
             t-attf-style="grid-row: {{rowPosition}}; grid-column: {{columnsGap}};"
        >
            <a class="btn btn-link o_text_overflow"
               t-on-click="() => this.onCreateInlineClick(row?.section)"
               data-hotkey="i">
               <i class="fa fa-plus-circle fs-4 me-1"/>Add a line
            </a>
        </div>
        <div t-foreach="props.columns"
             t-as="column"
             t-key="column.id"
             class="o_grid_row o_grid_add_line position-relative"
             t-att-class="{
                   'o_grid_cell_today' : column.isToday,
                   'bg-view': isEven,
                }"
             t-att-data-grid-row="rowPosition"
             t-att-data-grid-column="column_index + 1 + columnsGap"
             t-attf-style="grid-row: {{rowPosition}}; grid-column: {{column_index + 1 + columnsGap}};"
        >
            <div class="position-absolute top-0 start-0 w-100 h-100"
                 t-att-class="{
                    'o_grid_cell_overlay_today bg-info bg-opacity-25' : column.isToday,
                    'o_grid_cell_overlay bg-700' : !column.isToday,
                 }"
                 t-attf-class="{{getUnavailableClass(column)}}"
            />
        </div>
        <div class="o_grid_add_line position-md-sticky end-0 text-bg-200"
             t-attf-style="grid-row: {{rowPosition}}; grid-column: {{props.columns.length + 1 + columnsGap}};"
        ></div>
    </t>
    <t t-name="web_grid.Footer">
        <t t-if="!props.options.hideColumnTotal">
            <t t-set="rowPosition" t-value="getTotalRowPosition()"/>
            <t t-set="isEven" t-value="rowPosition % 2 !== 0"/>
            <div class=""
                 t-att-class="{
                    'bg-view': isEven,
                    'bg-100': !isEven,
                    'z-1 position-md-sticky start-0': !props.model.useSampleModel,
                 }"
                 t-attf-style="grid-row: {{rowPosition}}; grid-column: {{columnsGap}};"/>
            <t t-set="grandTotal"
               t-value="0"
            />
            <div t-foreach="props.columns"
                 t-as="column"
                 t-key="column.id"
                 class="o_grid_row o_grid_column_total o_grid_highlightable position-relative"
                 t-att-class="{
                    'bg-view': isEven and column.grandTotal &gt;= 0,
                    'text-danger': column.grandTotal lt 0,
                 }"
                 t-att-data-grid-row="rowPosition"
                 t-att-data-grid-column="column_index + 1 + columnsGap"
                 t-attf-style="grid-row: {{rowPosition}}; grid-column: {{column_index + 1 + columnsGap}};"
            >
                <div class="position-absolute top-0 start-0 w-100 h-100"
                     t-att-class="{
                        'o_grid_cell_overlay_today bg-info bg-opacity-25' : column.isToday,
                        'o_grid_cell_overlay bg-700' : !column.isToday,
                     }"
                     t-attf-class="{{getUnavailableClass(column)}}"/>
                <t t-set="grandTotal"
                   t-value="grandTotal + column.grandTotal"
                />
                <div class="h-100 d-flex justify-content-center align-items-center"
                     t-if="column.grandTotal !== 0">
                    <div class="o_grid_bar_chart_total_title">
                        <span class="fs-5 fw-bolder"
                              t-att-class="{
                                'text-danger' : getUnavailableClass(column) === 'o_grid_unavailable' and column.grandTotal gt 0
                              }">
                            <t t-esc="formatValue(column.grandTotal)"/>
                        </span>
                    </div>
                </div>
            </div>
            <div t-if="!props.options.hideLineTotal"
                t-att-data-grid-row="rowPosition"
                t-att-data-grid-column="props.columns.length + 1 + columnsGap"
                t-attf-class="o_grid_highlightable position-md-sticky end-0 d-flex align-items-center justify-content-center text-black fw-bold {{ getFooterTotalCellClasses(grandTotal) }}"
                t-attf-style="grid-row: {{rowPosition}}; grid-column: {{props.columns.length + 1 + columnsGap}};"
            >
                <span>
                    <t t-esc="formatValue(grandTotal)"/>
                </span>
            </div>
        </t>
    </t>
    <t t-name="web_grid.barChart">
        <t t-if="!props.options.hideColumnTotal">
            <t t-set="rowPosition" t-value="getTotalRowPosition()"/>
            <t t-set="isEven" t-value="(rowPosition + 1) % 2 !== 0"/>
            <div class="o_grid_row_barChart"
                 t-att-class="{
                    'bg-view': isEven,
                    'bg-100': !isEven,
                    'z-1 position-md-sticky start-0': !props.model.useSampleModel,
                 }"
                 t-attf-style="grid-row: {{rowPosition + 1}}; grid-column: {{columnsGap}};"/>
            <t t-set="grandTotal" t-value="0"/>
            <div t-foreach="props.columns"
                t-as="column"
                t-key="column.id"
                class="o_grid_row o_grid_column_total o_grid_highlightable o_grid_bar_chart_container"
                t-att-class="{'bg-view': isEven}"
                t-att-data-grid-row="rowPosition + 1"
                t-att-data-grid-column="column_index + 1 + columnsGap"
                t-attf-style="grid-row: {{rowPosition + 1}}; grid-column: {{column_index + 1 + columnsGap}};"
            >
                <t t-set="grandTotal" t-value="grandTotal + column.grandTotal"/>
                <div class="h-100 position-relative"
                     t-if="column.grandTotal !== 0">
                    <div class="o_grid_bar_chart_total_pill position-absolute w-100 border-top border-primary border-2 bg-primary bg-opacity-50" t-if="props.options.hasBarChartTotal"
                         t-att-style="getColumnBarChartHeightStyle(column)"
                    >
                    </div>
                </div>
            </div>
            <div t-if="!props.options.hideLineTotal"
                 class="o_grid_row o_grid_column_total o_grid_row_total o_grid_highlightable position-md-sticky end-0 bg-200"
                 t-att-data-grid-row="rowPosition + 1"
                 t-att-data-grid-column="props.columns.length + 1 + columnsGap"
                 t-attf-style="grid-row: {{rowPosition + 1}}; grid-column: {{props.columns.length + 1 + columnsGap}};"
            >
                <div class="h-100 position-relative"
                     t-if="grandTotal !== 0"/>
            </div>
        </t>
    </t>
    <t t-name="web_grid.NavigationButtons">
        <div class="o_grid_navigation_buttons position-md-sticky d-flex gap-2" t-if="props.model.columnFieldIsDate">
            <button class="btn btn-secondary"
                    data-hotkey="t"
                    type="button"
                    t-on-click="onTodayButtonClick"
            >
                Today
            </button>
            <div class="btn-group">
                <button class="btn btn-secondary"
                        type="button"
                        t-on-click="onPreviousButtonClick"
                        data-hotkey="p"
                >
                    <span aria-label="Previous" class="oi oi-arrow-left" role="img" title="Previous"/>
                </button>
                <ViewScaleSelector
                        t-if="rangesArray.length"
                        scales="props.ranges"
                        currentScale="props.state.activeRangeName"
                        setScale.bind="onRangeClick"
                        isWeekendVisible="props.state.isWeekendVisible"
                        toggleWeekendVisibility="props.toggleWeekendVisibility"
                />
                <button type="button"
                        class="btn btn-secondary"
                        t-on-click="onNextButtonClick"
                        data-hotkey="n"
                >
                    <span aria-label="Next" class="oi oi-arrow-right" role="img" title="Next"/>
                </button>
            </div>
        </div>
    </t>
</templates>
